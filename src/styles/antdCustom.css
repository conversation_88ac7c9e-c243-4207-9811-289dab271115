.custom-select-dropdown,
.custom-select-selection-item,
.custom-select-selection-search-input {
  font-family: var(--font-family) !important;
}

.custom-select-selector {
  border: 1px solid var(--input-border) !important;
  min-height: 40px !important;
}

.custom-select-multiple .custom-select-selection-wrap {
  align-self: normal !important;
}

.custom-select-outlined.custom-select-multiple .custom-select-selection-item {
  background-color: var(--primary-25) !important;
  color: var(--primary-600) !important;
  border: 1px solid var(--primary-100) !important;
  font-weight: 400;
  font-size: 14px;
  height: auto !important;
  padding: 3px 8px !important;
  border-radius: 6px !important;
}

.custom-select-selection-item-remove {
  color: #96a9b1 !important;
}

.custom-date-picker,
.custom-time-picker {
  height: 40px !important;
  width: 100% !important;
}

.custom-time-picker .custom-time-picker-input>input,
.custom-date-picker .custom-time-picker-input>input {
  font-family: var(--font-family);
}

.custom-date-picker-dropdown .custom-date-picker-cell-in-view.custom-date-picker-cell-selected:not(.custom-date-picker-cell-disabled) .custom-date-picker-cell-inner {
  background-color: var(--primary-600);
}

.custom-date-picker-dropdown .custom-date-picker-cell-in-view.custom-date-picker-cell-range-end:not(.custom-date-picker-cell-disabled) .custom-date-picker-cell-inner,
.custom-date-picker-dropdown .custom-date-picker-cell-in-view.custom-date-picker-cell-range-start:not(.custom-date-picker-cell-disabled) .custom-date-picker-cell-inner {
  background-color: var(--primary-600);
}

.custom-date-picker-dropdown .custom-date-picker-cell-in-view.custom-date-picker-cell-today .custom-date-picker-cell-inner::before {
  border: 1px solid var(--primary-600);
}

.custom-date-picker-dropdown .custom-date-picker-header-view>button:hover,
.custom-date-picker-dropdown a:hover,
.custom-time-picker-dropdown a:hover {
  color: var(--primary-600) !important;
}

.avoid-tab-position .ant-tabs-content {
  position: inherit !important;
}

.custom-select-single {
  height: 100% !important;
}

.custom-antd-outlined,
.custom-antd-outlined:hover {
  @apply !rounded-lg !border-[#96A9B1] !text-inherit;
}

/* Form styling */
.custom-form .ant-form-item {
  margin-bottom: 0px !important;
}

.custom-form .ant-form-item-label {
  margin-bottom: 10px;
  font-family: var(--font-family);
  font-weight: 500;
  padding: 0 0 5px !important;
}

.custom-form .ant-form-item-label>label {
  display: flex !important;
  flex-direction: row-reverse !important;
  justify-content: flex-end !important;
  width: fit-content !important;
  gap: 4px !important;
  color: var(--black-text);
}

.custom-form .ant-select-selection-item,
.custom-form .ant-select-item-option {
  font-family: var(--font-family);
}

.custom-form .form-fields-wrapper {
  margin-top: 12px;
}

.custom-form .ant-input {
  font-family: var(--font-family);
  height: 40px;
  border-radius: 8px;
}

.combined-input .ant-form-item:last-child {
  margin-top: 27px;
}

.combined-input .ant-input-group>.ant-input:last-child {
  border-radius: 0px !important;
  border-right: transparent;
}

.custom-form textarea {
  min-height: 80px !important;
  max-height: 200px !important;
}

.custom-form .large-textarea {
  min-height: 94px !important;
  max-height: 250px !important;
}

.custom-form .ant-form-item .ant-form-item-label>label::after {
  display: none;
}

.custom-form .ant-form-item-explain-error {
  font-family: var(--font-family);
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.custom-form .ant-input-group-addon:first-child {
  background: #f8fbfc;
  height: 40px;
  padding: 0px;
  @apply 3xsm:w-[40%] md:w-[35%] lg:w-[35%] 2xl:w-[20%];
}

.custom-form .ant-form-item-horizontal .ant-form-item-label {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

.custom-form .switch-form-item {
  width: 50%;
  background-color: var(--primary-25);
  border-radius: 6px;
}

.custom-form .ant-form-item-control {
  flex-grow: 0;
  flex: none;
}

.custom-form .switch-form-item .ant-row {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 4px 10px 4px 10px;
  @apply sm:gap-4;
}

.custom-form .switch-form-item .ant-form-item-label>label {
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
}

.ant-btn:hover .btn-icon {
  stroke: #f8fbfc;
}

.ant-btn:hover .btn-icon-fill {
  fill: #f8fbfc;
}