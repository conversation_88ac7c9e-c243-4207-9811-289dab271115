export interface ICategories {
  id?: string;
  name: string;
}

export interface IPermissionsForContacts {
  prices: boolean;
  address: boolean;
  invoices: boolean;
}

export interface IPackages {
  id: string;
  orderId: string;
  packageTemplateId: string;
  packageTemplateName: string;
  itemType: string;
  quantity: number;
  weight: number;
  weightUnit: string;
  length: number;
  width: number;
  height: number;
  dimensionUnit: string;
  volume: number;
  declaredValue: number;
  description: string;
  notes: string;
  imageUrl: string;
  createdAt: string;
  updatedAt: string;
}

export interface IOrder {
  id: string;
  tenantId: string;
  trackingNumber: string;
  referenceNumber: string;
  status: 'Draft' | 'Confirmed' | 'In_Transit' | 'Delivered' | string; // extend as needed
  customerId: string;
  customerName: string;
  requestedById: string;
  requestedByName: string;
  collectionAddressId: string;
  collectionAddressSummary: string;
  collectionContactName: string;
  collectionInstructions: string;
  collectionSignatureRequired: boolean;
  scheduledCollectionTime: string; // ISO date string
  actualCollectionTime: string; // ISO date string
  deliveryAddressId: string;
  deliveryAddressSummary: string;
  deliveryContactName: string;
  deliveryInstructions: string;
  deliverySignatureRequired: boolean;
  scheduledDeliveryTime: string; // ISO date string
  actualDeliveryTime: string; // ISO date string
  packageTemplateId: string;
  packageTemplateName: string;
  totalItems: number;
  totalWeight: number;
  totalVolume: number;
  declaredValue: number;
  assignedDriverId: string;
  assignedDriverName: string;
  assignedVehicleId: string;
  assignedVehicleDescription: string;
  basePrice: number;
  optionsPrice: number;
  miscAdjustment: number;
  customerAdjustment: number;
  totalPrice: number;
  billingStatus: 'Not_Billed' | 'Billed' | string;
  paymentStatus: 'Pending' | 'Paid' | 'Failed' | string;
  distance: number;
  distanceUnit: string;
  estimatedDuration: string; // e.g., "01:30:00"
  description: string;
  comments: string;
  internalNotes: string;
  items: IPackages[];
  createdAt: string;
  updatedAt: string;
}

export interface OrderPaginatedResponse {
  data: IOrder[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}
