import { AxiosInstance } from 'axios';
import { ApiResponse, PaginationParams, PaginatedResponse, FilterParams } from './types';

export class BaseService<T, CreateDTO = Partial<T>, UpdateDTO = Partial<T>> {
  protected axios: AxiosInstance;
  protected endpoint: string;

  constructor(axios: AxiosInstance, endpoint: string) {
    this.axios = axios;
    this.endpoint = endpoint;
  }

  async getByEntity(entity: string, params?: PaginationParams): Promise<PaginatedResponse<T>> {
    const response = await this.axios.get<ApiResponse<PaginatedResponse<T>>>(`${entity}`, {
      params,
    });
    return response.data as unknown as PaginatedResponse<T>;
  }

  async getList<D = PaginatedResponse<T>>(params?: PaginationParams & FilterParams): Promise<D> {
    const response = await this.axios.get<ApiResponse<D>>(this.endpoint, {
      params,
    });
    return response.data as unknown as D;
  }

  async getById<D = T>(
    id: string | number,
    params?: (PaginationParams & FilterParams) | undefined
  ): Promise<D> {
    const response = await this.axios.get<D>(`${this.endpoint}/${id}`, { params });
    return response.data;
  }

  async create(data: CreateDTO): Promise<T> {
    const response = await this.axios.post<T>(this.endpoint, data);
    return response.data;
  }
  async createByEntity(data: CreateDTO, entity: string): Promise<T> {
    const response = await this.axios.post<T>(`${this.endpoint}/${entity}`, data);
    return response.data;
  }
  async createById(data: CreateDTO, id: string, entity: string): Promise<T> {
    const response = await this.axios.post<T>(`${this.endpoint}/${id}/${entity}`, data);
    return response.data;
  }

  async update<D = UpdateDTO>(id: string | number, data: D): Promise<T> {
    const response = await this.axios.put<ApiResponse<T>>(`${this.endpoint}/${id}`, data);
    return response.data.data;
  }

  async delete(id: string | number): Promise<void> {
    await this.axios.delete(`${this.endpoint}/${id}`);
  }

  async duplicate(id: string | number): Promise<void> {
    await this.axios.post<ApiResponse<T>>(`${this.endpoint}/${id}/duplicate`);
  }

  async getListById<D = T>(
    id: string,
    params?: PaginationParams & FilterParams
  ): Promise<PaginatedResponse<D>> {
    const response = await this.axios.get<PaginatedResponse<D>>(`${this.endpoint}/${id}`, {
      params,
    });

    return response.data;
  }
}
