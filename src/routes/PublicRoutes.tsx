import { ROUTES } from '@/constant/RoutesConstant';
import { getStorageItem } from '@/lib/Storage';
import { StorageKeys } from '@/types/enums/StorageEnums';
import { Navigate } from 'react-router-dom';

interface PublicRouteProps {
  children: JSX.Element;
}

export const PublicRoute: React.FC<PublicRouteProps> = ({ children }) => {
  const isAuthenticated = getStorageItem(StorageKeys.IS_AUTHENTICATED);
  return !isAuthenticated ? children : <Navigate to={ROUTES.ORDER.ORDER_DETAILS} />;
};
