import { calculateTime } from './dateHelper';
import { searchData } from './searchQuery';
import { isFormChangedHandler } from './formHelper';
import { getPaginationData } from './agGridHelper';

export const getOrderStatusClassName = (status: string): string => {
  console.log('Status', status);
  switch (status) {
    case 'Draft':
      return 'grey-chip';
    case 'Cancelled':
      return 'error-chip';
    case 'In_Progress':
    case 'Pending':
      return 'warning-chip';
    case 'Assigned':
      return 'primary-chip';
    case 'Completed':
      return 'success-chip';
    default:
      return '';
  }
};

export { calculateTime, searchData, isFormChangedHandler, getPaginationData };
