import { ConfigSchema } from '@customTypes/ConfigSchema';

const developmentConfig: ConfigSchema = {
  appName: 'Transport App',
  apiUrl: 'http://localhost:3000',
  baseUrl: 'http://localhost:3000',
  showDetailedErrors: true,
  notification: {
    duration: 5,
    placement: 'topRight',
    pauseOnHover: false,
    className: 'notify-wrapper',
  },
  AxiosDefaultConfig: {
    timeout: 10000,
    retries: 3,
    retryDelay: 1000,
    maxRefreshAttempt: 3,
  },
  ContextMenuConfig: {
    minimumSubMenuSize: 190,
  },
  dateFormate: 'DD/MM/YYYY hh:mm A',
  dateFormateWithoutTime: 'DD/MM/YYYY',
  timeFormate12: 'hh:mm A',
  timeFormate24: 'HH:mm',
  is12HoursFormate: false,
  maxPriceSetScheduleLimit: 15,
  minPasswordLength: 8,
  maxPasswordLength: 16,
  otpLength: 4,
  units: {
    distance: 'km',
    weight: 'lb',
  },
};

export default developmentConfig;
