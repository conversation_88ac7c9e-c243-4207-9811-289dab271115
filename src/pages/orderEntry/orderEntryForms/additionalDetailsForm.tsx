import { Form, Input, Upload, Button, Row, Col, Switch, Space, Typography, InputNumber } from 'antd';
import { UploadOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { useState } from 'react';
import { useLanguage } from '@/hooks/useLanguage';

const { TextArea } = Input;
const { Text } = Typography;

interface IAdditionalDetailsFormProps {
  form: any
}

const AdditionalDetailsForm: React.FC<IAdditionalDetailsFormProps> = (props) => {
  const { form } = props
  const { t } = useLanguage();
  const [showCashOnDelivery, setShowCashOnDelivery] = useState(false);
  const [showInsurance, setShowInsurance] = useState(false);

  return (
    <Form form={form} layout="vertical" requiredMark={false} className="additional-details-form">
      <div className="mb-4">
        <Row gutter={16} className="mb-4">
          <Col xs={24} md={12}>
            <div className="flex w-[98%] justify-between items-center mb-2">
              <div className="flex items-center">
                <Text>Cash on delivery</Text>
                <InfoCircleOutlined className="ml-1 text-gray-400" />
              </div>
              <Switch
                checked={showCashOnDelivery}
                onChange={setShowCashOnDelivery}
                className="order-collapse-switch "
              />
            </div>
            {showCashOnDelivery && (
              <Form.Item
                className='address-form-item'
                name={'amount'}
                rules={[

                  {
                    validator: (_, value) => {
                      if (!value) return;
                      if (value > 999999999) {
                        return Promise.reject(
                          new Error(t('priceModifiers.maximumValueExceeded'))
                        );
                      } else if (value < 0) {
                        return Promise.reject(
                          new Error(
                            t('priceModifiers.configureTiersForm.pleaseEnterValidNumber')
                          )
                        );
                      } else {
                        return Promise.resolve();
                      }
                    },
                  },
                ]}
              >
                <InputNumber
                  maxLength={9}
                  className="address-select-item" min={0}
                  addonBefore="$"
                  placeholder="0.00"
                  precision={2}
                />
              </Form.Item>
            )}
          </Col>

          <Col xs={24} md={12}>
            <div className="flex justify-between items-center mb-2">
              <div className="flex items-center">
                <Text>Insurance</Text>
                <InfoCircleOutlined className="ml-1 text-gray-400" />
              </div>
              <Switch
                checked={showInsurance}
                onChange={setShowInsurance}
                className="order-collapse-switch "
              />
            </div>
            {showInsurance && (
              <Form.Item
                className='address-form-item'
                name={'insuranceAmount'}
                rules={[

                  {
                    validator: (_, value) => {
                      if (!value) return;
                      if (value > 999999999) {
                        return Promise.reject(
                          new Error(t('priceModifiers.maximumValueExceeded'))
                        );
                      } else if (value < 0) {
                        return Promise.reject(
                          new Error(
                            t('priceModifiers.configureTiersForm.pleaseEnterValidNumber')
                          )
                        );
                      } else {
                        return Promise.resolve();
                      }
                    },
                  },
                ]}
              >
                <InputNumber
                  maxLength={9}
                  className="address-select-item" min={0}
                  addonBefore="$"
                  placeholder="0.00"
                  precision={2}
                />
              </Form.Item>
            )}
          </Col>
        </Row>

        <div className="mb-2">
          <Text>Special Instruction</Text>
        </div>
        <Form.Item name="specialInstruction" noStyle>
          <TextArea
            placeholder="Entrance is on the left side of the building"
            autoSize={{ minRows: 3, maxRows: 6 }}
            className="rounded-md w-full"
          />
        </Form.Item>
      </div>

    </Form>
  );
};

export default AdditionalDetailsForm;


