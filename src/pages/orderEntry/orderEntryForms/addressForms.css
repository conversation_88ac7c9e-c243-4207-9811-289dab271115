/* Order Form Container */
.order-form-container {
  display: flex;
  width: 100%;
  overflow: hidden;
  background-color: #fff;
  border-radius: 8px;
}

.order-form-row {
  width: 100%;
  height: 100%;
}

/* Forms Column */
.forms-column {
  height: 100%;
}

.scrollable-forms-container {
  height: 100%;
  overflow-y: auto;
}

/* For Chrome, Edge, and Safari */
.scrollable-forms-container::-webkit-scrollbar {
  width: 8px;
}

.scrollable-forms-container::-webkit-scrollbar-track {
  background: #fff;
}

.scrollable-forms-container::-webkit-scrollbar-thumb {
  background-color: #fff;
  border-radius: 4px;
}

/* Checkout Column */
.checkout-column {
  height: 100%;
}

/* Form Collapse Styles */
.form-collapse {
  width: 100%;
  background-color: #fff;
  border-radius: 8px;
}

.form-collapse>.ant-collapse-item>.ant-collapse-header {
  font-weight: 500;
  font-size: 16px;
  padding: 0 !important;
}

.form-collapse>.ant-collapse-item {
  padding-top: 2px;
}

.form-collapse>.ant-collapse-item>.ant-collapse-content {
  border-top: 0px !important;
}

.form-collapse>.ant-collapse-item>.ant-collapse-header>.ant-collapse-expand-icon {
  padding-inline-end: 0px !important;
  padding-inline-start: 0px !important;
  height: 48px;
  padding-right: 15px;
  background-color: var(--primary-25);
  border-bottom: 1px solid #e5e7eb;
  width: 40px;
}

.address-select-item {
  height: 40px;
}

.address-form-item>.ant-form-item-row>.ant-form-item-label>label {
  display: flex;
  flex-direction: row-reverse;
  justify-content: flex-end;
  gap: 5px;
}

.address-form-item>.ant-form-item-row>.ant-form-item-label>label::after {
  display: none;
}

.combined-masked-input .ant-input-group>.ant-input:last-child {
  border-radius: 0px !important;
  border-right: transparent;
}

.address-form-maskedItem>.ant-form-item-row>.ant-form-item-label>label {
  display: flex;
  flex-direction: row-reverse;
  justify-content: flex-end;
  gap: 5px;
}

.address-form-maskedItem>.ant-form-item-row>.ant-form-item-label {
  font-size: 14px;
  font-weight: 500;
}

.address-form-maskedItem>.ant-form-item-row>.ant-form-item-label>label::after {
  display: none;
}

.address-form-maskedItem>.ant-input-wrapper>.ant-input-group-addon {
  background: #f8fbfc;
  height: 40px;
  @apply 3xsm:w-[40%] md:w-[35%] lg:w-[35%] 2xl:w-[20%];
}

.address-form-maskedItem>.ant-input-wrapper>input {
  height: 40px;
}

.orders-general-datepicker-dropdown>.ant-picker-panel-container>.ant-picker-panel-layout>div>.ant-picker-footer>.ant-picker-ranges>.ant-picker-ok>.ant-btn {
  background-color: var(--primary-600) !important;
  border-color: var(--primary-600) !important;
  padding: 5px 10px !important;
}

.orders-general-datepicker-dropdown>.ant-picker-panel-container>.ant-picker-panel-layout>div .ant-picker-panel>.ant-picker-datetime-panel>.ant-picker-date-panel>.ant-picker-body>.ant-picker-content>tbody>tr>.ant-picker-cell-selected>.ant-picker-cell-inner {
  background-color: var(--primary-600) !important;
}

.general-form-item-prefix {
  @apply mb-0 h-[38px];
}

/* Form Styles */
.pickup-address-form,
.delivery-address-form,
.timing-form,
.package-details-form,
.additional-details-form {
  padding: 8px;
}

/* Right Column Styles */
.right-column {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: sticky;
  top: 0;
  gap: 10px;
}

.right-column>.ant-collapse>.ant-collapse-item>.ant-collapse-content>.ant-collapse-content-box {
  padding: 0px !important;
}


.delivery-services-card,
.payment-summary-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: none;
}

.delivery-services-card .ant-card-head,
.payment-summary-card .ant-card-head {
  background-color: var(--primary-25);
  padding: 12px 16px;
  min-height: 48px;
}

.delivery-services-card .ant-card-head-title,
.payment-summary-card .ant-card-head-title {
  padding: 0;
}

.delivery-services-header,
.payment-summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.delivery-services-icon,
.payment-summary-icon {
  font-size: 12px;
  transform: rotate(180deg);
}

.delivery-services-card .ant-card-body,
.payment-summary-card .ant-card-body {
  padding: 0;
}

.delivery-services-list {
  display: flex;
  flex-direction: column;
}

.delivery-service-item {
  padding: 10px 10px;
  border-bottom: 1px solid #f0f0f0;
}

.delivery-service-item:last-child {
  border-bottom: none;
}

.delivery-service-radio {
  width: 100%;
  margin-right: 0;
}

.delivery-service-radio>span:nth-child(2) {
  @apply w-full text-primary-600;
}

.delivery-service-radio .ant-radio {
  top: 4px;
}

.delivery-service-radio>.ant-radio-checked>.ant-radio-inner {
  background-color: var(--primary-600) !important;
}

.delivery-service-content {
  margin-left: 8px;
  width: 100%;
}

.delivery-service-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.delivery-service-name {
  font-size: 16px;
  color: #000;
  font-weight: normal;
}

.delivery-service-price {
  font-size: 16px;
  color: var(--primary-500);
  font-weight: 500;
}

.delivery-service-time {
  font-size: 14px;
  color: var(--primary-500);
}

/* Payment Summary Styles */
.payment-summary-content {
  padding: 16px;
  background-color: #fff;
}

.payment-summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.payment-summary-label {
  font-size: 14px;
  color: #000;
}

.payment-summary-value {
  font-size: 14px;
  color: #000;
  text-align: right;
}

.payment-summary-divider {
  height: 1px;
  background-color: #f0f0f0;
  margin: 12px 0;
}

.payment-summary-row.total-row {
  margin-bottom: 0;
}

.payment-summary-label.total,
.payment-summary-value.total {
  font-weight: 600;
}

.payment-summary-value.total {
  color: #000;
}

/* Button styles */
.order-button-container {
  margin-top: 16px;
}

.place-order-btn {
  background-color: var(--primary-600);
  border-color: var(--primary-600);
  height: 48px;
  font-size: 16px;
  border-radius: 8px;
}

.place-order-btn:hover {
  background-color: var(--primary-600) !important;
  border-color: var(--primary-600) !important;
}

.place-order-btn:disabled {
  background-color: var(--disabled-background) !important;
  border-color: var(--disabled-background) !important;
}

.orders-general-datepicker-dropdown>.ant-picker-panel-container>.ant-picker-panel-layout>div>.ant-picker-footer>.ant-picker-ranges>.ant-picker-ok>.ant-btn:disabled {
  background-color: var(--disabled-background) !important;
  border-color: var(--disabled-background) !important;
}

.order-actions-container {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
}

.save-draft-btn {
  width: 48%;
  height: 40px;
  border-color: #d0d5dd;
  color: #000;
  background-color: white;
  border-radius: 8px;
}

.discard-order-btn {
  width: 48%;
  height: 40px;
  border-color: #d0d5dd;
  color: #f04438;
  background-color: white;
  border-radius: 8px;
}

.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.mb-2 {
  margin-bottom: 8px;
}

.mt-4 {
  margin-top: 16px;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .order-form-container {
    flex-direction: column;
    height: auto !important;
  }

  .forms-column,
  .checkout-column {
    height: auto;
  }

  .scrollable-forms-container {
    height: auto;
    max-height: 60vh;
    overflow-y: auto;
    padding-right: 0;
  }

  .form-collapse {
    margin-bottom: 16px;
  }

  .right-column {
    position: static;
    margin-top: 24px;
  }
}

/* Package form specific styles */
.package-header {
  margin-bottom: 16px;
}

.package-number {
  padding: 4px 12px;
  border: 1px solid var(--primary-500);
  border-radius: 4px;
  font-weight: 500;
  color: var(--primary-500);
}

.upload-button {
  width: 100%;
  text-align: left;
  height: 40px;
  display: flex;
  align-items: center;
}

.address-select-item {
  width: 100%;
}

.address-select-item>.ant-input-number-input-wrap {
  height: 100%;
}

.address-select-item>.ant-input-number-input-wrap>input {
  height: 100%;
}

.address-select-item>.ant-input-number-wrapper {
  height: 100%;
}

.address-select-item>.ant-input-number-wrapper>.ant-input-number>.ant-input-number-input-wrap {
  height: 100%;
}

.address-select-item>.ant-input-number-wrapper>.ant-input-number>.ant-input-number-input-wrap>input {
  height: 100%;
}

/* Package details specific styles */
.package-details-container {
  width: 100%;
}

.package-card {
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.package-number {
  padding: 4px 12px;
  border: 1px solid var(--primary-500);
  border-radius: 4px;
  font-weight: 500;
  color: var(--primary-500);
  display: inline-block;
}

.field-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.field-value {
  font-size: 14px;
  font-weight: 500;
}

.remove-package-btn {
  color: #ef4444;
}

.remove-package-btn:hover {
  color: #dc2626;
  background: rgba(239, 68, 68, 0.05);
}

.services-collapse>.ant-collapse-item>.ant-collapse-content {
  height: fit-content;
  max-height: 400px;
  overflow-y: auto;
}

.region-payment-class>.ant-select-selector:focus {
  border-color: #e5e7eb !important;
  box-shadow: none !important;
}

.payment-summary-container>.ant-card>.ant-card-head {
  border-bottom: 2px solid #e5e7eb !important;
}

.package-form-collapse>.ant-collapse-item>.ant-collapse-content>.ant-collapse-content-box {
  padding: 0 !important;
}

.package-details-form {
  padding: 0 !important;
}

.order-collapse-switch.ant-switch-checked {
  background-color: #2d3484 !important;
  border-color: #2d3484 !important;
}

.package-form-collapse {
  width: 100%;
  background-color: #fff;
  border-radius: 8px;
}

.package-form-collapse>.ant-collapse-item>.ant-collapse-header {
  font-weight: 500;
  font-size: 16px;
  padding: 0 !important;
}

.package-form-collapse>.ant-collapse-item {
  padding-top: 2px;
}

.package-form-collapse>.ant-collapse-item>.ant-collapse-content {
  border-top: 0px !important;
}

.package-form-collapse>.ant-collapse-item>.ant-collapse-header>.ant-collapse-expand-icon {
  padding-inline-end: 0px !important;
  padding-inline-start: 0px !important;
  height: 48px;
  padding-right: 15px;
  background-color: var(--primary-25);
  border-bottom: 1px solid #e5e7eb;
  width: 40px;
}