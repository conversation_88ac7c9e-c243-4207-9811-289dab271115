import React from 'react';
import { Typography, Card } from 'antd';

const { Text } = Typography;

interface PaymentSummaryProps {
  subtotal?: number;
  deliveryCharges?: number;
  taxDetails?: number;
  total?: number;
}

const PaymentSummary: React.FC<PaymentSummaryProps> = ({
  subtotal,
  deliveryCharges,
  taxDetails,
  total,
}) => {
  return (
    <div className="payment-summary-container ">
      <Card
        title={
          <div className="payment-summary-header">
            <span>Payment summary</span>
          </div>
        }
        className="payment-summary-card"
        bordered={true}
      >
        <div className="payment-summary-content !bg-primary-25">
          <div className="payment-summary-row">
            <Text className="payment-summary-label">Delivery Charges</Text>
            <Text className="payment-summary-value">$****</Text>
          </div>
          <div className="payment-summary-row">
            <Text className="payment-summary-label">Subtotal</Text>
            <Text className="payment-summary-value">$****</Text>
          </div>
          <div className="payment-summary-row">
            <Text className="payment-summary-label">Tax Details</Text>
            <Text className="payment-summary-value">$****</Text>
          </div>

          <div className="payment-summary-divider"></div>

          <div className="payment-summary-row total-row">
            <Text className="payment-summary-label total">Total</Text>
            <Text className="payment-summary-value total">${total?.toFixed(2)}</Text>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default PaymentSummary;
