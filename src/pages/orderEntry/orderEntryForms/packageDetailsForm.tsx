import { Form, Select, InputNumber, Row, Col, Button, Input, message, Divider } from 'antd';
import { useState, useEffect } from 'react';
import './addressForms.css';
import { EditOutlined, InfoCircleOutlined } from '@ant-design/icons';
import CustomUpload from '@/components/common/customUpload/CustomUpload';
import { DeleteIcon } from '@/assets';
import { useLanguage } from '@/hooks/useLanguage';

const { Option } = Select;
const MAX_TOTAL_SIZE_MB = 20;
const MAX_TOTAL_SIZE_BYTES = MAX_TOTAL_SIZE_MB * 1024 * 1024; // 20MB in bytes

interface IPackageFormDetailsForm {
  setPackageFormDetails: (details: any[]) => void;
}
const PackageDetailsForm: React.FC<IPackageFormDetailsForm> = (props) => {
  const { setPackageFormDetails } = props;
  const { t } = useLanguage();
  const [form] = Form.useForm();
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [filePreviewMap, setFilePreviewMap] = useState<Record<number, any[]>>({});
  const [isInitialForm, setIsInitialForm] = useState<boolean>(true);
  const [shouldScroll, setShouldScroll] = useState(false);

  useEffect(() => {
    const formValues = form.getFieldsValue();
    if (formValues.packages) {
      setPackageFormDetails(formValues.packages);
    }
  }, [form.getFieldValue('packages')]);
  useEffect(() => {
    const formValues = form.getFieldsValue();
    if (formValues.packages) {
      const newFilePreviewMap: Record<number, any[]> = {};
      formValues.packages.forEach((pkg: any, index: number) => {
        if (pkg?.files?.fileList) {
          newFilePreviewMap[index] = pkg.files.fileList;
        }
      });
      setFilePreviewMap(newFilePreviewMap);
    }
  }, []);

  const validateFiles = (_: any, value: any) => {
    if (!value || !value.fileList || value.fileList.length === 0) {
      return Promise.reject(new Error(t('orderEntryForms.packageDetails.errors.uploadRequired')));
    }

    const totalSize = value.fileList.reduce((sum: number, file: any) => {
      return sum + (file.originFileObj?.size || 0);
    }, 0);

    if (totalSize > MAX_TOTAL_SIZE_BYTES) {
      return Promise.reject(
        new Error(t('orderEntryForms.packageDetails.errors.fileSizeExceeded', {
          size: (totalSize / (1024 * 1024)).toFixed(2),
          limit: MAX_TOTAL_SIZE_MB
        }))
      );
    }

    return Promise.resolve();
  };

  const toggleEdit = (index: number) => {
    if (editingIndex === index) {
      setEditingIndex(null);
    } else {
      setEditingIndex(index);
    }
  };
  const updateCubicDimension = (index: number) => {
    const length = form.getFieldValue(['packages', index, 'length']);
    const width = form.getFieldValue(['packages', index, 'width']);
    const height = form.getFieldValue(['packages', index, 'height']);

    const cubicDimension = `${length || 'Length'} × ${width || 'Width'} × ${height || 'Height'}`;
    form.setFieldValue(['packages', index, 'cubicDimension'], cubicDimension);
  };

  // Custom handler for file changes
  const handleFileChange = (info: any, fieldPath: any) => {
    const { fileList } = info;

    form.setFieldValue(fieldPath, {
      file: fileList[fileList.length - 1],
      fileList
    });

    const packageIndex = fieldPath[1];
    setFilePreviewMap(prev => ({
      ...prev,
      [packageIndex]: fileList
    }));

    const totalSize = fileList.reduce((sum: number, file: any) => {
      return sum + (file.originFileObj?.size || 0);
    }, 0);

    if (totalSize > MAX_TOTAL_SIZE_BYTES) {
      message.warning(t('orderEntryForms.packageDetails.warnings.fileSizeExceeded', {
        size: (totalSize / (1024 * 1024)).toFixed(2),
        limit: MAX_TOTAL_SIZE_MB
      }));
    }

    form.validateFields([fieldPath]);
  };

  const handleFileDelete = (packageIndex: number, fileIndex: number) => {
    const currentFiles = filePreviewMap[packageIndex] || [];
    if (currentFiles.length === 0) return;

    const newFileList = [...currentFiles];
    newFileList.splice(fileIndex || currentFiles.length - 1, 1);

    const fieldPath = ['packages', packageIndex, 'files'];
    form.setFieldValue(fieldPath, {
      file: newFileList.length > 0 ? newFileList[newFileList.length - 1] : null,
      fileList: newFileList
    });

    setFilePreviewMap({
      ...filePreviewMap,
      [packageIndex]: newFileList
    });

    form.validateFields([fieldPath]);
  };

  const handleRemovePackage = (index: number) => {
    setFilePreviewMap(prev => {
      const newMap = { ...prev };
      delete newMap[index];
      const updatedMap: Record<number, any[]> = {};
      Object.keys(newMap).forEach(key => {
        const numKey = parseInt(key);
        if (numKey > index) {
          updatedMap[numKey - 1] = newMap[numKey];
        } else {
          updatedMap[numKey] = newMap[numKey];
        }
      });

      return updatedMap;
    });
  };

  useEffect(() => {
    const packageCount = form.getFieldValue('packages')?.length || 0;
    setShouldScroll(packageCount > 5);
  }, [form.getFieldValue('packages')?.length]);

  return (
    <Form
      className='package-details-form'
      form={form}
      initialValues={{ packages: [{}] }}
      layout="vertical"
    >
      <div
        className={shouldScroll ? "max-h-[500px] overflow-y-auto pr-2" : ""}
        style={{
          scrollbarWidth: 'thin',
          scrollbarColor: '#d1d5db transparent'
        }}
      >
        <Form.List name="packages">
          {(fields, { add, remove }) => (
            <>
              {fields.map((field, index) => {
                const isEditing = editingIndex === index || (editingIndex === null && index === fields.length - 1);
                const fieldPath: string = ['packages', index, 'files'] as unknown as string;

                return (
                  <div key={field.key} className="mb-6 bg-white rounded-lg p-[24px] pb-[0px]">
                    <div className="flex justify-between items-center mb-4">
                      <div className="flex items-center">
                        {(index !== fields.length - 1 || editingIndex === null || editingIndex === index) && (
                          <span className="border border-primary-600 rounded-md text-primary-600 text-sm font-medium px-3 py-1">
                            {t('orderEntryForms.packageDetails.package')} {index + 1}
                          </span>
                        )}
                      </div>
                      {!isEditing && !isInitialForm && index !== fields.length - 1 && (
                        <div className="flex items-center gap-3">
                          <Button
                            type="text"
                            icon={<EditOutlined />}
                            onClick={() => toggleEdit(index)}
                            className="mr-2 text-[#333] border border-primary-100 rounded-md"
                          />
                          <Button
                            disabled={fields.length === 0}
                            danger
                            icon={<DeleteIcon />}
                            type="text"
                            onClick={() => {
                              handleRemovePackage(index);
                              remove(index)
                            }}
                            className="text-error-600 p-2 border border-error-500 rounded-md"
                          >
                            {t('common.buttons.remove')}
                          </Button>
                        </div>
                      )}

                      {isEditing && editingIndex === index && (
                        <div className="flex items-center gap-3">
                          <Button
                            type="text"
                            onClick={() => toggleEdit(index)}
                            className="text-[#333] border border-primary-100 rounded-md"
                          >
                            {t('common.buttons.cancel')}
                          </Button>
                          <Button
                            type="text"
                            onClick={() => toggleEdit(index)}
                            className="text-[#333] bg-primary-600 text-white border border-primary-600 rounded-md"
                          >
                            {t('common.buttons.update')}
                          </Button>
                        </div>
                      )}
                    </div>

                    {isEditing ? (
                      <>
                        <Row gutter={16} className="mb-4">
                          <Col xs={24} md={12}>
                            <Form.Item
                              className='address-form-item'
                              {...field}
                              name={[field.name, 'packageType']}
                              fieldKey={[field.fieldKey as number, 'packageType']}
                              label={<span className="text-sm font-medium">{t('orderEntryForms.packageDetails.packagingType')}</span>}
                              rules={[{ required: true, message: t('orderEntryForms.packageDetails.errors.packageTypeRequired') }]}
                            >
                              <Select placeholder={t('orderEntryForms.packageDetails.selectPackageType')} className="address-select-item">
                                <Option value="Box">{t('orderEntryForms.packageDetails.packageTypes.box')}</Option>
                                <Option value="Envelope">{t('orderEntryForms.packageDetails.packageTypes.envelope')}</Option>
                                <Option value="Pallet">{t('orderEntryForms.packageDetails.packageTypes.pallet')}</Option>
                                <Option value="Other">{t('orderEntryForms.packageDetails.packageTypes.other')}</Option>
                              </Select>
                            </Form.Item>
                          </Col>
                          <Col xs={24} md={12}>
                            <Form.Item
                              className='address-form-item'
                              {...field}
                              name={[field.name, 'quantity']}
                              fieldKey={[field.fieldKey as number, 'quantity']}
                              label={<span className="text-sm font-medium">{t('orderEntryForms.packageDetails.quantity')}</span>}
                              rules={[{ required: true, message: t('orderEntryForms.packageDetails.errors.quantityRequired') },
                              {
                                validator: (_, value) => {
                                  if (!value) return;
                                  if (value > 999999999) {
                                    return Promise.reject(
                                      new Error(t('priceModifiers.maximumValueExceeded'))
                                    );
                                  } else if (value < 0) {
                                    return Promise.reject(
                                      new Error(
                                        t('priceModifiers.configureTiersForm.pleaseEnterValidNumber')
                                      )
                                    );
                                  } else {
                                    return Promise.resolve();
                                  }
                                },
                              },]}

                            >
                              <InputNumber
                                maxLength={9}
                                className="address-select-item" min={1}
                                placeholder="1"
                              />
                            </Form.Item>
                          </Col>
                        </Row>

                        <Row gutter={16} className="mb-4">
                          <Col xs={24} md={12}>
                            <Form.Item
                              dependencies={['length', 'width', 'height']}
                              className='address-form-item'
                              {...field}
                              name={[field.name, 'cubicDimension']}
                              fieldKey={[field.fieldKey as number, 'cubicDimension']}
                              label={<span className="text-sm font-medium">{t('orderEntryForms.packageDetails.cubicDimension')}</span>}
                            >
                              <Input
                                className="address-select-item"
                                placeholder={t('orderEntryForms.packageDetails.dimensionsPlaceholder')}
                                disabled
                              />
                            </Form.Item>
                          </Col>

                          <Col xs={24} md={12}>
                            <Form.Item
                              className='address-form-item'
                              {...field}
                              name={[field.name, 'totalWeight']}
                              fieldKey={[field.fieldKey as number, 'totalWeight']}
                              label={<span className="text-sm font-medium">{t('orderEntryForms.packageDetails.combinedWeight')}</span>}
                              rules={[{ required: true, message: t('orderEntryForms.packageDetails.errors.weightRequired') },
                              {
                                validator: (_, value) => {
                                  if (!value) return;
                                  if (value > 999999999) {
                                    return Promise.reject(
                                      new Error(t('priceModifiers.maximumValueExceeded'))
                                    );
                                  } else if (value < 0) {
                                    return Promise.reject(
                                      new Error(
                                        t('priceModifiers.configureTiersForm.pleaseEnterValidNumber')
                                      )
                                    );
                                  } else {
                                    return Promise.resolve();
                                  }
                                },
                              },]}
                            >
                              <InputNumber
                                maxLength={9}
                                className="address-select-item" min={0}
                                step={0.1}
                                placeholder="0.0"
                              />
                            </Form.Item>
                          </Col>
                        </Row>

                        <Row gutter={16} className="mb-4">
                          <Col xs={24} md={12}>
                            <Form.Item
                              className='address-form-item'
                              {...field}
                              name={[field.name, 'length']}
                              fieldKey={[field.fieldKey as number, 'length']}
                              label={<span className="text-sm font-medium">{t('orderEntryForms.packageDetails.length')}</span>}
                              rules={[{ required: true, message: t('orderEntryForms.packageDetails.errors.lengthRequired') },
                              {
                                validator: (_, value) => {
                                  if (!value) return;
                                  if (value > 999999999) {
                                    return Promise.reject(
                                      new Error(t('priceModifiers.maximumValueExceeded'))
                                    );
                                  } else if (value < 0) {
                                    return Promise.reject(
                                      new Error(
                                        t('priceModifiers.configureTiersForm.pleaseEnterValidNumber')
                                      )
                                    );
                                  } else {
                                    return Promise.resolve();
                                  }
                                },
                              },]}
                            >
                              <InputNumber
                                maxLength={9}
                                className="address-select-item" min={0}
                                placeholder="0"
                                onChange={() => {
                                  updateCubicDimension(index);
                                  form.validateFields();
                                }}
                              />
                            </Form.Item>
                          </Col>

                          <Col xs={24} md={12}>
                            <Form.Item
                              className='address-form-item'
                              {...field}
                              name={[field.name, 'width']}
                              fieldKey={[field.fieldKey as number, 'width']}
                              label={<span className="text-sm font-medium">{t('orderEntryForms.packageDetails.width')}</span>}
                              rules={[{ required: true, message: t('orderEntryForms.packageDetails.errors.widthRequired') },
                              {
                                validator: (_, value) => {
                                  if (!value) return;
                                  if (value > 999999999) {
                                    return Promise.reject(
                                      new Error(t('priceModifiers.maximumValueExceeded'))
                                    );
                                  } else if (value < 0) {
                                    return Promise.reject(
                                      new Error(
                                        t('priceModifiers.configureTiersForm.pleaseEnterValidNumber')
                                      )
                                    );
                                  } else {
                                    return Promise.resolve();
                                  }
                                },
                              },]}
                            >
                              <InputNumber
                                maxLength={9}
                                className="address-select-item" min={0}
                                placeholder="0"
                                onChange={() => {
                                  updateCubicDimension(index);
                                  form.validateFields();
                                }}
                              />
                            </Form.Item>
                          </Col>
                        </Row>

                        <Row gutter={16} className="mb-4">
                          <Col xs={24} md={12}>
                            <Form.Item
                              className='address-form-item'
                              {...field}
                              name={[field.name, 'height']}
                              fieldKey={[field.fieldKey as number, 'height']}
                              label={<span className="text-sm font-medium">{t('orderEntryForms.packageDetails.height')}</span>}
                              rules={[{ required: true, message: t('orderEntryForms.packageDetails.errors.heightRequired') },
                              {
                                validator: (_, value) => {
                                  if (!value) return;
                                  if (value > 999999999) {
                                    return Promise.reject(
                                      new Error(t('priceModifiers.maximumValueExceeded'))
                                    );
                                  } else if (value < 0) {
                                    return Promise.reject(
                                      new Error(
                                        t('priceModifiers.configureTiersForm.pleaseEnterValidNumber')
                                      )
                                    );
                                  } else {
                                    return Promise.resolve();
                                  }
                                },
                              },]}
                            >
                              <InputNumber
                                maxLength={9}
                                className="address-select-item" min={0}
                                placeholder="0"
                                onChange={() => {
                                  updateCubicDimension(index);
                                  form.validateFields();
                                }}
                              />
                            </Form.Item>
                          </Col>

                          <Col xs={24} md={12}>
                            <Form.Item
                              className='address-form-item'
                              {...field}
                              name={[field.name, 'declaredValue']}
                              fieldKey={[field.fieldKey as number, 'declaredValue']}
                              label={<span className="text-sm font-medium">{t('orderEntryForms.packageDetails.declaredValue')}</span>}
                            >
                              <InputNumber
                                className="address-select-item" min={0}
                                addonBefore="$"
                                placeholder="0.00"
                                precision={2}
                              />
                            </Form.Item>
                          </Col>
                        </Row>

                        <Row gutter={16}>
                          <Col span={24}>
                            <Form.Item
                              className='address-form-item !mb-0'
                              {...field}
                              name={[field.name, 'files']}
                              fieldKey={[field.fieldKey as number, 'files']}
                              label={
                                <div className="flex items-center">
                                  <span className="text-sm font-medium mr-1">{t('orderEntryForms.packageDetails.uploadImage')}</span>
                                  <InfoCircleOutlined className="text-gray-400" />
                                </div>
                              }
                              rules={[
                                {
                                  validator: validateFiles,
                                  required: true,
                                  message: t('orderEntryForms.packageDetails.errors.uploadRequired'),
                                },
                              ]}
                              validateTrigger={['onChange', 'onBlur']}
                            >
                              <CustomUpload
                                label={' '}
                                form={form}
                                name={fieldPath}
                                placeholder={t('orderEntryForms.packageDetails.uploadImagePlaceholder')}
                                uploadButtonText={t('common.buttons.upload')}
                                uploadComponentProps={{
                                  maxCount: 5,
                                  multiple: true,
                                  listType: "picture",
                                  beforeUpload: () => false,
                                  onChange: (info) => handleFileChange(info, fieldPath),
                                  showUploadList: false,
                                }}
                              />
                            </Form.Item>
                          </Col>
                        </Row>

                        {filePreviewMap[index] && filePreviewMap[index].length > 0 && (
                          <div className="mt-2">
                            <div className="flex flex-wrap gap-2">
                              {filePreviewMap[index].map((file: any, fileIndex: number) => (
                                <div key={fileIndex} className="relative w-16 h-16 border rounded p-1">
                                  <div
                                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center cursor-pointer"
                                    onClick={() => handleFileDelete(index, fileIndex)}
                                  >
                                    ×
                                  </div>
                                  <img
                                    src={file.url || URL.createObjectURL(file.originFileObj)}
                                    alt={`preview ${fileIndex}`}
                                    className="w-full h-full object-contain"
                                  />
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </>
                    ) : !isEditing && editingIndex !== index && index !== fields.length - 1 && (

                      <div className="border-0 p-0">
                        <Row gutter={[16, 16]}>
                          <Col span={5}>
                            <div className="text-[16px] font-[600]">{t('orderEntryForms.packageDetails.packageType')}</div>
                            <div className="text-[16px]">
                              {form.getFieldValue(['packages', index, 'packageType']) || '-'}
                            </div>
                          </Col>
                          <Divider type="vertical" className="h-8 !bg-primmay-100" />
                          <Col span={5}>
                            <div className="text-[16px] font-[600]">{t('orderEntryForms.packageDetails.quantity')}</div>
                            <div className="text-[16px]">
                              {form.getFieldValue(['packages', index, 'quantity']) || '-'}
                            </div>
                          </Col>
                          <Divider type="vertical" className="h-8 !bg-primmay-100" />

                          <Col span={5}>
                            <div className="text-[16px] font-[600]">{t('orderEntryForms.packageDetails.cubicDimension')}</div>
                            <div className="text-[16px]">
                              {form.getFieldValue(['packages', index, 'cubicDimension']) || '-'}
                            </div>
                          </Col>
                          <Divider type="vertical" className="h-8 !bg-primmay-100" />

                          <Col span={5}>
                            <div className="text-[16px] font-[600]">{t('orderEntryForms.packageDetails.combinedWeight')} <span className='text-[14px] font-[400] text-gray-500'>(Kg)</span></div>
                            <div className="text-[16px]">
                              {form.getFieldValue(['packages', index, 'totalWeight']) || '-'}
                            </div>
                          </Col>
                        </Row>
                        {filePreviewMap[index] && filePreviewMap[index].length > 0 && (
                          <div className="mt-4">
                            <div className="text-[16px] font-[600] mb-2">{t('orderEntryForms.packageDetails.uploadedImages')}</div>
                            <div className="flex flex-wrap gap-2">
                              {filePreviewMap[index].map((file: any, fileIndex: number) => (
                                <div key={fileIndex} className="w-16 h-16 border rounded p-1">
                                  <img
                                    src={file.url || URL.createObjectURL(file.originFileObj)}
                                    alt={`preview ${fileIndex}`}
                                    className="w-full h-full object-contain"
                                  />
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                    {(index !== fields.length - 1) && (
                      <Divider className="!bg-primmay-100" />)}

                  </div>
                );
              })}

              <div className="w-full bg-primary-25 p-3 flex justify-end mt-4">
                <Button
                  disabled={editingIndex != null}
                  type="primary"
                  onClick={() => {
                    setIsInitialForm(false);
                    const lastIndex = fields.length - 1;
                    form.validateFields([
                      ['packages', lastIndex, 'packageType'],
                      ['packages', lastIndex, 'quantity'],
                      ['packages', lastIndex, 'totalWeight'],
                      ['packages', lastIndex, 'length'],
                      ['packages', lastIndex, 'width'],
                      ['packages', lastIndex, 'height'],
                      ['packages', lastIndex, 'files']
                    ]).then(() => {
                      add();
                    }).catch(err => {
                      console.error('Validation failed:', err);
                    });
                  }}
                  className="bg-[#2E3A8C] text-white hover:!bg-[#2E3A8C] hover:opacity-90 rounded-md p-2 py-4"
                >
                  {t('orderEntryForms.packageDetails.addMorePackage')}
                </Button>
              </div>
            </>
          )}
        </Form.List>
      </div>
    </Form>
  );
};

export default PackageDetailsForm;
