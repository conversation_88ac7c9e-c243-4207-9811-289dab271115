import { useState, useEffect } from 'react';
import { Row, Col, Collapse, Button, Form, Checkbox } from 'antd';
import {
  PickupAddressForm,
  DeliveryAddressForm,
  TimingForm,
  PackageDetailsForm,
  AdditionalDetailsForm,
  AvailableDeliveryServices,
  PaymentSummary,
} from './index';
import './addressForms.css';
import { CollapseDownIcon, CollapseUpIcon, EmailOutlinedIcon, PhoneOutlinedIcon } from '@/assets';
import { addressServiceHook } from '@/api/address/useAddress';
import { GetAddressDto } from '@/api/address/address.types';
import { scheduleServiceHook } from '@/api/service/useService';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { useLanguage } from '@/hooks/useLanguage';
import PaymentsPage from '../payments';
import { IGetAvailableServices } from '@/api/service/service.types';
import { ordersServiceHook } from '@/api/order/useOrders';
import { IOrder } from '@/api/order/order.types';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from '@/constant/RoutesConstant';

const { Panel } = Collapse;

const OrderForm = () => {
  const { t } = useLanguage();
  const [form] = Form.useForm();
  const [additionalDetailsForm] = Form.useForm();

  const [selectedDeliveryService, setSelectedDeliveryService] = useState<IGetAvailableServices | null>(null);
  const [containerHeight, setContainerHeight] = useState('calc(100vh - 180px)');
  const [packageFormDetails, setPackageFormDetails] = useState<any[]>([]);
  const [isAbleToPlaceOrder, setIsAbleToPlaceOrder] = useState(false);
  const [isPlacedOrder, setIsPlacedOrder] = useState(false);
  const [isActiveForm, setIsActiveForm] = useState({
    pickupAddress: true,
    deliveryAddress: true,
    timing: false,
    packageDetails: false,
    additionalDetails: false,
  });
  const [selectedDateAndTime, setSelectedDateAndTime] = useState('');
  const [pickUpAddressForm] = Form.useForm()
  const [isAllFilledUp, setIsAllFilledUp] = useState(false)
  const { data: allAddressesList, refetch, isRefetching } = addressServiceHook.useEntities('no-pagination');
  const [availableServices, setAvailableServices] = useState<any>()
  const [selectedAddresses, setSelectedAddresses] = useState<{
    pickupAddress: GetAddressDto | null;
    deliveryAddress: GetAddressDto | null;
  }>({
    pickupAddress: null,
    deliveryAddress: null,
  });
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [hasFirstCompleteSubmission, setHasFirstCompleteSubmission] = useState(false);
  const navigate = useNavigate();
  useEffect(() => {
    const updateHeight = () => {
      const headerHeight = 120;
      const footerHeight = 53;
      const windowHeight = window.innerHeight;
      setContainerHeight(`calc(${windowHeight}px - ${headerHeight}px - ${footerHeight}px)`);
    };
    updateHeight();
    window.addEventListener('resize', updateHeight);
    return () => {
      window.removeEventListener('resize', updateHeight);
    };
  }, []);

  const getServicesByPickUpDate = scheduleServiceHook.useCreate({})
  const fetchServices = async (dateAndTime: string, pricing: boolean, orderDetails: any) => {
    const response = await getServicesByPickUpDate.mutateAsync({
      pickupDate: dateAndTime,
      includePricing: pricing,
      order: orderDetails
    });
    if (response) {
      setAvailableServices(response);
    }
  };
  useEffect(() => {
    const currentDateAndTime = new Date().toISOString();

    if (isInitialLoad) {
      fetchServices(currentDateAndTime, false, {});
      setIsInitialLoad(false);
    }
  }, []);
  useEffect(() => {
    const hasRequiredFields =
      selectedAddresses.pickupAddress !== null &&
      selectedAddresses.deliveryAddress !== null &&
      selectedDateAndTime !== '';

    if (!hasRequiredFields) {
      return;
    }

    const orderJSON = {
      "id": "test-order-1",
      "tenantId": "test-tenant-1",
      "trackingNumber": "TEST001",
      "referenceNumber": "REF001",
      "status": "Draft",
      "customerId": "customer-1",
      "collectionAddressId": selectedAddresses.pickupAddress?.id,
      "deliveryAddressId": selectedAddresses.deliveryAddress?.id,
      "originZoneId": "zone-1",
      "destinationZoneId": "zone-2",
      "scheduledCollectionTime": selectedDateAndTime,
      "scheduledDeliveryTime": "2024-02-01T14:00:00Z",
      "basePrice": 20.00,
      "totalItems": 2,
      "weight": 15.5,
      "distance": 25.3,
      "width": 40,
      "height": 30,
      "length": 50,
      "declaredPrice": 500.00,
      "items": packageFormDetails || []
    };

    if (!hasFirstCompleteSubmission && hasRequiredFields) {
      fetchServices(selectedDateAndTime, true, orderJSON);
      setHasFirstCompleteSubmission(true);
    } else if (hasFirstCompleteSubmission) {
      fetchServices(selectedDateAndTime, true, orderJSON);
    }
  }, [
    selectedAddresses.pickupAddress,
    selectedAddresses.deliveryAddress,
    selectedDateAndTime,
    packageFormDetails,
    hasFirstCompleteSubmission
  ]);
  useEffect(() => {
    if (selectedAddresses.pickupAddress && selectedAddresses.deliveryAddress && selectedDateAndTime !== '' && packageFormDetails && isAllFilledUp) {
      setIsAbleToPlaceOrder(true);
    }
  }, [selectedAddresses.pickupAddress, selectedAddresses.deliveryAddress, selectedDateAndTime, packageFormDetails, isAllFilledUp]);

  const createDraftMutation = ordersServiceHook.useCreateByEntity('draft', {
    onSuccess: () => {
      navigate(ROUTES.ORDER.ORDER_DETAILS)
    }
  })
  const handleSaveAsDraft = async () => {
    const dataToCreateDraft = {
      referenceNumber: '',
      collectionAddressId: selectedAddresses.pickupAddress?.id as string,
      collectionContactName: selectedAddresses.pickupAddress?.customer as string,
      collectionInstructions: selectedAddresses.pickupAddress?.notes as string,
      deliveryAddressId: selectedAddresses.deliveryAddress?.id as string,
      deliveryContactName: selectedAddresses.deliveryAddress?.customer as string,
      deliveryInstructions: selectedAddresses.deliveryAddress?.notes as string,
      description: additionalDetailsForm.getFieldValue('specialInstruction') as string,
      comments: ''

    }
    await createDraftMutation.mutateAsync(dataToCreateDraft as IOrder)
  };

  const onDiscardOrder = () => {
    try {
      form.resetFields();
      additionalDetailsForm.resetFields();
      setSelectedDeliveryService(null);
      setPackageFormDetails([]);
      setIsAbleToPlaceOrder(false);
      setIsPlacedOrder(false);
      setSelectedDateAndTime('');
      setIsAllFilledUp(false);
      setSelectedAddresses({
        pickupAddress: null,
        deliveryAddress: null,
      });
      setHasFirstCompleteSubmission(false);
      setAvailableServices(undefined);

    } catch (error) {
      console.error('Error in onDiscardOrder:', error);
    }
  };

  const handleDiscardOrder = () => {
    customAlert.error({
      title: t('orderEntryForms.discardOrder.title'),
      message: t('orderEntryForms.discardOrder.message'),
      firstButtonTitle: t('orderEntryForms.discardOrder.discardButton'),
      secondButtonTitle: t('common.buttons.cancel'),
      firstButtonFunction: () => {
        onDiscardOrder();
        customAlert.destroy();
      },
      secondButtonFunction: () => {
        customAlert.destroy();
      },
    });
  };
  return (
    <Form form={form} layout="vertical" className="order-form pl-5 py-6">
      <div className="order-form-container" style={{ height: containerHeight }}>
        <Row gutter={[24, 0]} className="order-form-row">
          <Col xs={24} lg={16} className="forms-column">
            <div className="scrollable-forms-container">
              <Collapse
                onChange={(key) => {
                  setIsActiveForm((prev) => ({
                    ...prev,
                    pickupAddress: key.includes('1'),
                  }));
                }}
                expandIconPosition="end"
                expandIcon={({ isActive }) => {
                  return (
                    <span className="text-[#2D3484] font-semibold text-[14px] bg-primary-25">
                      {isActive ? <CollapseDownIcon /> : <CollapseUpIcon />}
                    </span>
                  );
                }}
                defaultActiveKey={['1']}
                className="form-collapse"
              >
                <Panel
                  header={
                    <div className="flex flex-col">
                      <span
                        className={`bg-primary-25 h-[48px] font-[600] p-3 border-b border-[#e5e7eb] `}
                      >
                        {t('orderEntryForms.pickupAddress.title')}
                      </span>
                      {!isActiveForm.pickupAddress && selectedAddresses.pickupAddress && (
                        <div className="flex flex-col gap-1 p-3">
                          <span className="font-[600]">
                            {selectedAddresses.pickupAddress.companyName}
                          </span>
                          <span className="flex gap-2 items-center font-[600]">
                            {selectedAddresses.pickupAddress.customer} | <PhoneOutlinedIcon />{' '}
                            {selectedAddresses.pickupAddress.phoneNumber}
                            {selectedAddresses.pickupAddress.phoneExtension &&
                              ` ext. ${selectedAddresses.pickupAddress.phoneExtension}`}{' '}
                            | <EmailOutlinedIcon /> {selectedAddresses.pickupAddress.email}
                          </span>
                          <span>
                            {selectedAddresses.pickupAddress.addressLine1},{' '}
                            {selectedAddresses.pickupAddress.addressLine2},{' '}
                            {selectedAddresses.pickupAddress.city},{' '}
                            {selectedAddresses.pickupAddress.province},{' '}
                            {selectedAddresses.pickupAddress.country},{' '}
                            {selectedAddresses.pickupAddress.postalCode}
                          </span>
                        </div>
                      )}
                    </div>
                  }
                  key="1"
                >
                  <PickupAddressForm
                    addressesList={allAddressesList?.data || []}
                    selectedPickupAddress={selectedAddresses.pickupAddress}
                    setSelectedPickupAddress={setSelectedAddresses}
                    refetch={refetch}
                    isRefetching={isRefetching}
                    form={pickUpAddressForm}
                  />
                </Panel>
              </Collapse>

              <Collapse
                onChange={(key) => {
                  setIsActiveForm((prev) => ({
                    ...prev,
                    deliveryAddress: key.includes('1'),
                  }));
                }}
                expandIconPosition="end"
                expandIcon={({ isActive }) => {
                  return (
                    <span className="text-[#2D3484] font-semibold text-[14px]">
                      {isActive ? <CollapseDownIcon /> : <CollapseUpIcon />}
                    </span>
                  );
                }}
                defaultActiveKey={['1']}
                className="form-collapse mt-4"
              >
                <Panel
                  header={
                    <div className="flex flex-col">
                      <span
                        className={`bg-primary-25 h-[48px] font-[600] p-3 border-b border-[#e5e7eb] `}
                      >
                        {t('orderEntryForms.deliveryAddress.title')}
                      </span>
                      {!isActiveForm.deliveryAddress && selectedAddresses.deliveryAddress && (
                        <div className="flex flex-col gap-1 p-3">
                          <span className="font-[600]">
                            {selectedAddresses.deliveryAddress.companyName}
                          </span>
                          <span className="flex gap-2 items-center font-[600]">
                            {selectedAddresses.deliveryAddress.customer} | <PhoneOutlinedIcon />{' '}
                            {selectedAddresses.deliveryAddress.phoneNumber}
                            {selectedAddresses.deliveryAddress.phoneExtension &&
                              ` ext. ${selectedAddresses.deliveryAddress.phoneExtension}`}{' '}
                            | <EmailOutlinedIcon /> {selectedAddresses.deliveryAddress.email}
                          </span>
                          <span>
                            {selectedAddresses.deliveryAddress.addressLine1},{' '}
                            {selectedAddresses.deliveryAddress.addressLine2},{' '}
                            {selectedAddresses.deliveryAddress.city},{' '}
                            {selectedAddresses.deliveryAddress.province},{' '}
                            {selectedAddresses.deliveryAddress.country},{' '}
                            {selectedAddresses.deliveryAddress.postalCode}
                          </span>
                        </div>
                      )}
                    </div>
                  }
                  key="1"
                >
                  <DeliveryAddressForm
                    addressesList={allAddressesList?.data || []}
                    selectedDeliveryAddress={selectedAddresses.deliveryAddress}
                    setSelectedDeliveryAddress={setSelectedAddresses}
                    refetch={refetch}
                  />
                </Panel>
              </Collapse>

              <Collapse
                expandIconPosition="end"
                expandIcon={({ isActive }) => {
                  return (
                    <span className="text-[#2D3484] font-semibold text-[14px]">
                      {isActive ? <CollapseDownIcon /> : <CollapseUpIcon />}
                    </span>
                  );
                }}
                defaultActiveKey={['1']}
                className="form-collapse mt-4"
              >
                <Panel
                  header={
                    <div className="flex flex-col">
                      <span
                        className={`bg-primary-25 h-[48px] font-[600] p-3 border-b border-[#e5e7eb] `}
                      >
                        {t('orderEntryForms.timing.title')}
                      </span>
                    </div>
                  }
                  key="1"
                >
                  <TimingForm setSelectedDateAndTime={setSelectedDateAndTime} />
                </Panel>
              </Collapse>

              <Collapse
                expandIconPosition="end"
                expandIcon={({ isActive }) => {
                  return (
                    <span className="text-[#2D3484] font-semibold text-[14px]">
                      {isActive ? <CollapseDownIcon /> : <CollapseUpIcon />}
                    </span>
                  );
                }}
                defaultActiveKey={['1']}
                className="package-form-collapse mt-4"
              >
                <Panel
                  header={
                    <div className="flex flex-col">
                      <span
                        className={`bg-primary-25 h-[48px] font-[600] p-3 border-b border-[#e5e7eb] `}
                      >
                        {t('orderEntryForms.packageDetails.title')}
                      </span>
                    </div>
                  }
                  key="1"
                >
                  <PackageDetailsForm setPackageFormDetails={setPackageFormDetails} />
                </Panel>
              </Collapse>

              <Collapse
                expandIconPosition="end"
                expandIcon={({ isActive }) => {
                  return (
                    <span className="text-[#2D3484] font-semibold text-[14px]">
                      {isActive ? <CollapseDownIcon /> : <CollapseUpIcon />}
                    </span>
                  );
                }}
                defaultActiveKey={['1']}
                className="form-collapse mt-4"
              >
                <Panel
                  header={
                    <div className="flex flex-col">
                      <span
                        className={`bg-primary-25 h-[48px] font-[600] p-3 border-b border-[#e5e7eb] `}
                      >
                        {t('orderEntryForms.additionalDetails.title')}
                      </span>
                    </div>
                  }
                  key="1"
                >
                  <AdditionalDetailsForm form={additionalDetailsForm} />
                </Panel>
              </Collapse>
            </div>
          </Col>
          <Col xs={24} lg={8} className="checkout-column">
            <div className="right-column scrollable-forms-container">
              <AvailableDeliveryServices
                availableServices={availableServices?.data || []}
                value={selectedDeliveryService?.id}
                setSelectedDeliveryService={setSelectedDeliveryService}
                getServicesStatus={getServicesByPickUpDate?.status}
              />

              {selectedDeliveryService?.pricing?.modifiers && selectedDeliveryService.pricing.modifiers.length > 0 && (
                <Collapse
                  expandIconPosition="end"
                  expandIcon={({ isActive }) => {
                    return (
                      <span className="text-[#2D3484] font-semibold text-[14px]">
                        {isActive ? <CollapseDownIcon /> : <CollapseUpIcon />}
                      </span>
                    );
                  }}
                  defaultActiveKey={['1']}
                  className="form-collapse mt-4"
                >
                  <Panel
                    header={
                      <div className="flex flex-col">
                        <span className="bg-primary-25 h-[48px] font-[600] p-3 border-b border-[#e5e7eb]">
                          Additional services
                        </span>
                      </div>
                    }
                    key="1"
                  >
                    <div className="flex flex-col max-h-[200px] overflow-y-auto">
                      {selectedDeliveryService.pricing.modifiers.map((modifier: any, index: number) => (
                        <div key={index} className="flex items-center gap-3 p-2 border rounded">
                          <Checkbox />
                          <label htmlFor={`modifier-${index}`} className="flex-1 font-medium cursor-pointer">
                            {modifier.name}
                          </label>
                          <span className="text-primary-600 font-semibold">${modifier.amount.toFixed(2)}</span>
                        </div>
                      ))}
                    </div>
                  </Panel>
                </Collapse>
              )}

              <PaymentSummary total={selectedDeliveryService?.pricing?.totalPrice && selectedDeliveryService?.pricing.totalPrice} />

              <PaymentsPage setIsAllFilledUp={setIsAllFilledUp} />

              <div className="order-button-container">
                <Button
                  onClick={() => {
                    setIsPlacedOrder(true);
                  }}
                  type="primary"
                  htmlType="submit"
                  block
                  className="place-order-btn"
                  disabled={!isAbleToPlaceOrder}
                >
                  {isPlacedOrder ? t('orderEntryForms.buttons.proceedToPay') : t('orderEntryForms.buttons.placeOrder')}
                </Button>
              </div>
              <div className="order-actions-container">
                <Button
                  disabled={!selectedAddresses.pickupAddress || !selectedAddresses.deliveryAddress}
                  onClick={handleSaveAsDraft}
                  className="save-draft-btn"
                >
                  {t('orderEntryForms.buttons.saveAsDraft')}
                </Button>
                <Button onClick={handleDiscardOrder} className="discard-order-btn">
                  {t('orderEntryForms.buttons.discardOrder')}
                </Button>
              </div>
            </div>
          </Col>
        </Row>
      </div>
    </Form>
  );
};

export default OrderForm;

