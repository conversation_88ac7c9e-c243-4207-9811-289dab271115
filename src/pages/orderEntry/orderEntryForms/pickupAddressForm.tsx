import { Form, Input, Select, Row, Col, Space, InputRef, message } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';
import './addressForms.css';
import { SearchForSelectIcon, StarForSelectFilledIcon, StarForSelectUnfilledIcon } from '@/assets';
import { formErrorRegex } from '@/constant/Regex';
import { MaskedInput } from 'antd-mask-input';
import {
  numberFieldValidator,
  validateCountryAndValue,
  validateMaskedInput,
} from '@/lib/FormValidators';
import { Dispatch, SetStateAction, useCallback, useEffect, useRef, useState } from 'react';
import { optionsForPrefix } from '@/constant/CountryCodeConstant';
import { MaskType } from 'antd-mask-input/build/main/lib/MaskedInput';
import { GetAddressDto } from '@/api/address/address.types';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { addressServiceHook } from '@/api/address/useAddress';
import TextArea from 'antd/es/input/TextArea';
import { FormInstance } from 'antd/lib';

interface IPickupAddressFormProps {
  addressesList: GetAddressDto[];
  selectedPickupAddress: GetAddressDto | null;
  setSelectedPickupAddress: Dispatch<
    SetStateAction<{ pickupAddress: GetAddressDto | null; deliveryAddress: GetAddressDto | null }>
  >;
  refetch: () => void;
  isRefetching: boolean;
  form: FormInstance
}

const PickupAddressForm: React.FC<IPickupAddressFormProps> = (props) => {
  const { addressesList, setSelectedPickupAddress, refetch, form } = props;
  const { t } = useLanguage();
  const notificationManager = useNotificationManager();
  const inputPhoneRef = useRef<InputRef>(null);

  const [allAddresses, setAllAddresses] = useState<GetAddressDto[]>([]);
  const [isOpen, setIsOpen] = useState<{ contactName: boolean; companyName: boolean }>({
    contactName: false,
    companyName: false,
  });

  const [maskPhoneInput, setMaskPhoneInput] = useState<{
    label: string;
    value: string;
    mask: MaskType;
    length: number;
  }>(optionsForPrefix[0]);

  useEffect(() => {
    setAllAddresses(addressesList);
  }, [addressesList]);

  const maskingInputPhone = useCallback((value: string, focus = true) => {
    const selectedCountryMask = optionsForPrefix?.find((item) => item.value === value);
    if (!selectedCountryMask) return;
    if (focus) {
      inputPhoneRef.current?.focus();
      setMaskPhoneInput(selectedCountryMask);
    }
  }, []);

  const favouriteAddressForDelivery = addressServiceHook.useUpdate({
    onSuccess: () => {
      notificationManager.success({
        message: t('orderEntryForms.address.success'),
        description: t('orderEntryForms.address.addedToFavoriteDelivery'),
      });
      refetch();
    },
  });

  const onFavouritePickupAddress = async (address: GetAddressDto) => {
    const dataToUpdate = {
      ...address,
      isFavoriteForPickup: !address.isFavoriteForPickup,
    };
    await favouriteAddressForDelivery.mutateAsync({ id: address.id, data: dataToUpdate });
  };

  const phoneNumberPrefix = (
    <Form.Item className="general-form-item-prefix !h-[38px]" name={'phoneCountryCode'}>
      <Select
        className="address-select-item"
        placeholder={t('common.usa')}
        options={optionsForPrefix}
        onChange={(value) => maskingInputPhone(value)}
      />
    </Form.Item>
  );

  const handleSelect = (selectedAddress: GetAddressDto) => {
    const phoneNumberFormat = selectedAddress?.phoneNumber.toString();

    form.setFieldsValue({
      contactName: selectedAddress.customer,
      companyName: selectedAddress.companyName,
      email: selectedAddress.email,
      phone: phoneNumberFormat,
      phoneExtension: selectedAddress.phoneExtension,
      addressLine1: selectedAddress.addressLine1,
      phoneCountryCode: selectedAddress.countryCode,
      addressLine2: selectedAddress.addressLine2,
      city: selectedAddress.city,
      province: selectedAddress.province,
      country: selectedAddress.country,
      postalCode: selectedAddress.postalCode,
    });
    setIsOpen({ contactName: false, companyName: false });
    setSelectedPickupAddress((prev) => ({
      ...prev,
      pickupAddress: selectedAddress,
    }));
  };

  return (
    <Form form={form} layout="vertical" className="pickup-address-form">
      <Row gutter={16}>
        <Col xs={24} md={12}>
          <Form.Item
            label={t('dashboard.customer.columns.formFieldNames.contactName')}
            name="contactName"
            rules={[{ required: true, message: t('orderEntryForms.address.contactNameRequired') }]}
            className="address-form-item"
          >
            <Select
              onSearch={(value) => {
                setAllAddresses(
                  addressesList.filter((address) =>
                    address.name.toLowerCase().includes(value.toLowerCase())
                  )
                );
              }}
              showSearch
              onSelect={(value) => {
                setAllAddresses(
                  addressesList.filter((address) =>
                    address.name.toLowerCase().includes(value.toLowerCase())
                  )
                );
              }}
              open={isOpen.contactName}
              onDropdownVisibleChange={(value) => setIsOpen({ ...isOpen, contactName: value })}
              dropdownRender={() => (
                <div className=' max-h-[300px] overflow-y-auto'>
                  {allAddresses?.length ? allAddresses.map((address) => {
                    const isSelected = props.selectedPickupAddress?.id === address.id;
                    return (
                      <div
                        key={address.id}
                        className={`flex justify-between items-center px-3 py-2 hover:bg-gray-100 cursor-pointer ${isSelected ? 'bg-primary-25 text-primary-600' : ''}`}
                        onClick={() => handleSelect(address)}
                      >

                        <span>
                          {address.name} ({address.customer})
                        </span>
                        <span
                          onClick={(e) => {
                            e.stopPropagation();
                            onFavouritePickupAddress(address);
                          }}
                          className="cursor-pointer"
                        >
                          {' '}
                          {address.isFavoriteForPickup ? (
                            <StarForSelectFilledIcon />
                          ) : (
                            <StarForSelectUnfilledIcon />
                          )}
                        </span>
                      </div>
                    );
                  }) : <span className='h-[35px] mx-2'>No Addresses are available to assign</span>}
                </div>
              )}
              className="address-select-item"
              placeholder={t('orderEntryForms.address.searchOrInputContactName')}
              suffixIcon={<SearchForSelectIcon />}
            />
          </Form.Item>
        </Col>

        <Col xs={24} md={12}>
          <Form.Item
            label={t('dashboard.customer.columns.formFieldNames.companyName')}
            name="companyName"
            rules={[{ required: true, message: t('orderEntryForms.address.companyNameRequired') }]}
            className="address-form-item"
          >
            <Select
              onSearch={(value) => {
                setAllAddresses(
                  addressesList.filter((address) =>
                    address.name.toLowerCase().includes(value.toLowerCase())
                  )
                );
              }}
              showSearch
              open={isOpen.companyName}
              onDropdownVisibleChange={(value) => setIsOpen({ ...isOpen, companyName: value })}
              dropdownRender={() => (
                <div className=' max-h-[300px] overflow-y-auto'>
                  {allAddresses?.length ? allAddresses
                    .map((address) => {
                      const isSelected = props.selectedPickupAddress?.id === address.id;
                      return (
                        <div
                          key={address.id}
                          className={`flex justify-between items-center px-3 py-2 hover:bg-gray-100 cursor-pointer ${isSelected ? 'bg-primary-25 text-primary-600' : ''}`}
                          onClick={() => handleSelect(address)}
                        >
                          <span>
                            {address.name} ({address.customer})
                          </span>
                          <span
                            onClick={(e) => {
                              e.stopPropagation();
                              onFavouritePickupAddress(address);
                            }}
                            className="cursor-pointer"
                          >
                            {' '}
                            {address.isFavoriteForDelivery ? (
                              <StarForSelectFilledIcon />
                            ) : (
                              <StarForSelectUnfilledIcon />
                            )}
                          </span>
                        </div>
                      );
                    }) : <span className='h-[35px] mx-2'>No Addresses are available to assign</span>}
                </div>
              )}
              className="address-select-item"
              placeholder={t('orderEntryForms.address.searchOrInputContactName')}
              suffixIcon={<SearchForSelectIcon />}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label={t('dashboard.customer.columns.formFieldNames.addressLine1')}
            name="addressLine1"
            rules={[{ required: true, message: t('customerAddressPage.operationalForm.addressLine1Error') }]}
            className="address-form-item"
          >
            <Input className="address-select-item" placeholder="123 Main St" />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            className="address-form-item"
            label={t('dashboard.customer.columns.formFieldNames.addressLine2')}
            name="addressLine2"
          >
            <Input className="address-select-item" placeholder="123 Main St" />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col xs={24} md={12}>
          <Form.Item
            className="address-form-item"
            label={t('addressPage.operationalForm.city')}
            name="city"
            rules={[{ required: true, message: t('customerAddressPage.operationalForm.cityError') }]}
          >
            <Input disabled placeholder={t('customerAddressPage.operationalForm.cityPlaceholder')} className="address-select-item" />
          </Form.Item>
        </Col>

        <Col xs={24} md={12}>
          <Form.Item
            className="address-form-item"
            label={t('addressPage.operationalForm.province')}
            name="province"
            rules={[{ required: true, message: t('customerAddressPage.operationalForm.provinceError') }]}
          >
            <Input disabled placeholder={t('customerAddressPage.operationalForm.provincePlaceholder')} className="address-select-item" />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col xs={24} md={12}>
          <Form.Item
            className="address-form-item"
            label={t('addressPage.operationalForm.country')}
            name="country"
            rules={[{ required: true, message: t('customerAddressPage.operationalForm.countryError') }]}
          >
            <Input disabled placeholder="Canada" className="address-select-item" />
          </Form.Item>
        </Col>

        <Col xs={24} md={12}>
          <Form.Item
            className="address-form-item"
            label={t('addressPage.operationalForm.postalCode')}
            name="postalCode"
            rules={[{ required: true, message: t('customerAddressPage.operationalForm.postalCodeError') }]}
          >
            <Input disabled placeholder="H3Z 2Y7" className="address-select-item" />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col xs={24} md={12}>
          <Form.Item
            className="address-form-item"
            label={t('addressPage.operationalForm.email')}
            name="email"
            rules={[
              { required: true, message: t('customerAddressPage.operationalForm.emailError') },
              {
                pattern: formErrorRegex.ValidEmailOrNot,
                message: t('customerAddressPage.operationalForm.emailTypeError'),
              },
            ]}
          >
            <Input placeholder="<EMAIL>" className="address-select-item" />
          </Form.Item>
        </Col>

        <Col xs={24} md={12}>
          <Space.Compact className="combined-masked-input address-form-maskedItem w-full">
            <Form.Item
              className="address-form-maskedItem w-full"
              dependencies={['phoneCountryCode']}
              validateFirst
              rules={[
                {
                  required: true,
                  validator: validateCountryAndValue(
                    form,
                    'phoneCountryCode',
                    'phone number',
                    true
                  ),
                },
                {
                  validator: (_, value) =>
                    validateMaskedInput(
                      value,
                      maskPhoneInput.length,
                      t('customerAddressPage.operationalForm.validPhoneNumberError')
                    ),
                },
              ]}
              name="phone"
              label={t('customerAddressPage.operationalForm.phoneNumber')}
            >
              <MaskedInput
                ref={inputPhoneRef}
                addonBefore={phoneNumberPrefix}
                className="address-form-maskedItem"
                placeholder={t('customerAddressPage.operationalForm.phoneNumberPlaceholder')}
                mask={maskPhoneInput.mask}
                onChange={(event) => form.setFieldValue('phone', event?.unmaskedValue)}
              />
            </Form.Item>
            <Form.Item name="phoneExtension" className="w-[25%] !mb-0">
              <Input
                onKeyDown={numberFieldValidator}
                placeholder="00000"
                maxLength={6}
                className="address-select-item mt-[30px]"
              />
            </Form.Item>
          </Space.Compact>
        </Col>
      </Row>

      <Row gutter={16}>
        {/* Notes */}
        <Col span={24}>
          <Form.Item label={t('customerAddressPage.operationalForm.comments')} name="notes">
            <TextArea
              placeholder={t('customerAddressPage.operationalForm.commentsPlaceHolder')}
              autoSize={{ minRows: 2, maxRows: 6 }}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default PickupAddressForm;


