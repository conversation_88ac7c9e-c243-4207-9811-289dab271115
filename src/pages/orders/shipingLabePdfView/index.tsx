import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/assets';
import React from 'react';
import Barcode from 'react-barcode';

const ShippingLabelPDFView: React.FC = () => {
  return (
    <div className="w-[600px] p-4 bg-white text-primary-900 shadow-lg mx-auto">
      {/* Header */}
      <div className="flex justify-between items-start mb-2">
        <div>
          <img src={LumigoLogo} alt="Lumigo" className="h-8 mb-2" />
          <p className="text-xs">
            VNP Solution 2015 Inc (VNP Transport)
            <br />
            Phone: ************
            <br />
            www.vnptransport.ca
          </p>
        </div>
        <div className="text-sm font-semibold">Shipping label</div>
      </div>

      {/* Sender / Receiver */}
      <div className="grid grid-cols-2 border border-primary-900 mb-2 text-sm">
        <div className="flex gap-2 p-2 pb-12 border-r border-r-primary-900">
          <h2 className="font-semibold mb-1">Sender:</h2>
          <p>
            NBS Printing, SAM,
            <br />
            7520 Chemin de la Côte-de-Liesse
            <br />
            Saint-Laurent
            <br />
            H4T 1E7
          </p>
        </div>
        <div className="flex gap-2 p-2 pb-4">
          <h2 className="font-semibold mb-1">Receiver:</h2>
          <p>
            MAJESTIX, NOOR,
            <br />
            340 Rue Alme-Vincent
            <br />
            ************
            <br />
            Vaudreuil-Dorion
            <br />
            J7V 5V5
          </p>
        </div>
      </div>

      {/* Tracking Section */}
      <div className="flex justify-between gap-2 mb-2 border border-primary-900 p-2">
        <div className="flex flex-col ga-2 items-start text-sm">
          <span className="font-semibold">Sameday</span>
          <span>Tracking : #147795</span>
        </div>
        <Barcode value="1245 4588 4545 10" className="w-[200px] h-[70px] text-lg" fontSize={30} />
      </div>

      {/* Package / Receiver Info */}
      <div className="grid grid-cols-2 text-sm border border-primary-900">
        <div className="p-2 pb-[150px] border-r border-r-primary-900 flex flex-col gap-2">
          <h2 className="font-semibold mb-1">Package details:</h2>
          <p>
            <strong>Description:</strong>
            <br />1 Package + 1 Box = Total: 2
          </p>
          <p>
            <strong>Dimensions:</strong>
            <br />
            0L x 0W x 0H
          </p>
          <p>
            <strong>Weight:</strong>
            <br />1
          </p>
        </div>
        <div className="p-2">
          <h2 className="font-semibold mb-1 pb-[150px]">Receiver information:</h2>
          {/* Add extra receiver notes if needed */}
        </div>
      </div>
    </div>
  );
};

export default ShippingLabelPDFView;
