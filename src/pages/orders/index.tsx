import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { useLanguage } from '@/hooks/useLanguage';
import {
  advanceFilterObjectMapper,
  highlightText,
  maskQuickFilterData,
} from '@/lib/SearchFilterTypeManage';
import { GridNames } from '@/types/AppEvents';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ICellRendererParams, IColDef, IExtendedSortChangedEvent } from '@/types/AgGridTypes';
import { getPaginationData } from '@/lib/helper';
import ColumnManage from '@/components/specific/columnManage';
import Icon from '@ant-design/icons';
import { DeleteIcon, EditPopupIcon, EyeIcon, NotificationBellIcon, PrinterIcon } from '@/assets';
import { Divider } from 'antd';
import { on } from '@/contexts/PulseContext';
import { AgGridReact } from 'ag-grid-react';
import { defaultPagination } from '@/constant/generalConstant';
import { IContextMenuItems, onContextMenuItemClickParams } from '@/types/ContextMenuTypes';
import { onSortChangeHandler } from '@/lib/helper/agGridHelper';
import { filterableModules } from '@/constant/AdvanceFilterConstant';
import ActiveFilters from '@/components/specific/activeFilters/ActiveFilters';
import { IAssignedFilters } from '@/components/specific/activeFilters/activeFiltersTypes';
import QuickFilter from '@/components/specific/quickFilter/QuickFilter';
import { ordersServiceHook } from '@/api/order/useOrders';
import { IOrder } from '@/api/order/order.types';
import { dateFormatter } from '@/lib/helper/dateHelper';
import { SendTrackingLinkIcon } from '@/assets/icons/sendTrackingLinkIcon';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from '@/constant/RoutesConstant';

const OrderListPage = () => {
  const [searchText, setSearchText] = useState('');
  const [filterParams, setFilterParams] = useState(defaultPagination);
  const [selectedQuickFilterData, setSelectedQuickFilterData] = useState<IAssignedFilters[]>([]);
  const { t } = useLanguage();

  const {
    data: order,
    isLoading,
    isFetching,
  } = ordersServiceHook.useList(filterParams, { staleTime: 30000, retry: 1 });

  const [orders, setOrders] = useState<IOrder[]>();
  const gridRef = useRef<AgGridReact<IOrder>>(null);
  const navigate = useNavigate();

  useEffect(() => {
    if (order) {
      setOrders(order.data);
    }
  }, [order]);

  const paginationData = useMemo(() => getPaginationData(order), [order]);

  const ordersContextMenuItems: IContextMenuItems[] = useMemo(() => {
    return [
      {
        label: t('ordersPage.contextMenu.view'),
        key: 'viewOrder',
        onClick: (params: onContextMenuItemClickParams) =>
          navigate(`${ROUTES.ORDER.LISTING}/${params.rowData.id}`),
        icon: EyeIcon,
      },
      {
        label: t('ordersPage.contextMenu.edit'),
        key: 'EditOrder',
        icon: EditPopupIcon,
        onClick: async () => {},
      },
      // TODO: Add approve order functionality in V2
      // {
      //   label: t('ordersPage.contextMenu.approveOrder'),
      //   icon: ApproveOrderIcon,
      //   key: 'unAssign',
      //   onClick: async (params: onContextMenuItemClickParams) => {
      //     params.closeContextMenu();
      //   },
      // },
      {
        label: t('ordersPage.contextMenu.sendTrackingLink'),
        icon: SendTrackingLinkIcon,
        key: 'sendTrackingLink',
      },
      {
        label: t('ordersPage.contextMenu.printLabel'),
        key: 'PrintLabel',
        icon: PrinterIcon,
        subMenu: [
          {
            label: t('ordersPage.contextMenu.printOptions.shippingBill'),
            key: 'shippingBill',
          },
          {
            label: t('ordersPage.contextMenu.printOptions.wayBill'),
            key: 'wayBill',
          },
          {
            label: t('ordersPage.contextMenu.printOptions.billOfLanding'),
            key: 'BillOfLanding',
          },
          {
            label: t('ordersPage.contextMenu.printOptions.orderSummary'),
            key: 'orderSummary',
          },
        ],
      },
      {
        label: t('ordersPage.sendNotification'),
        icon: NotificationBellIcon,
        key: 'SendNotification',
        subMenu: [
          {
            label: t('ordersPage.sendStatusToCustomer'),
            key: 'SendStatusToCustomer',
            subMenu: [
              {
                label: t('dashboard.customer.settings.settingsNotification.sms'),
                key: 'CustomerSms',
              },
              {
                label: t('dashboard.customer.settings.settingsNotification.email'),
                key: 'CustomerEmail',
              },
            ],
          },
          {
            label: t('ordersPage.sendStatusToReceiver'),
            key: 'SendStatusToReceiver',
            subMenu: [
              {
                label: t('dashboard.customer.settings.settingsNotification.sms'),
                key: 'CustomerSms',
              },
              {
                label: t('dashboard.customer.settings.settingsNotification.email'),
                key: 'CustomerEmail',
              },
            ],
          },
          {
            label: t('ordersPage.sendStatusToCollector'),
            key: 'SendStatusToCollector',
            subMenu: [
              {
                label: t('dashboard.customer.settings.settingsNotification.sms'),
                key: 'CustomerSms',
              },
              {
                label: t('dashboard.customer.settings.settingsNotification.email'),
                key: 'CustomerEmail',
              },
            ],
          },
          {
            label: t('ordersPage.sendStatusToDriver'),
            key: 'SendStatusToDriver',
            subMenu: [
              {
                label: t('dashboard.customer.settings.settingsNotification.sms'),
                key: 'CustomerSms',
              },
              {
                label: t('dashboard.customer.settings.settingsNotification.email'),
                key: 'CustomerEmail',
              },
            ],
          },
        ],
      },
      {
        label: <span className="text-red-500">{t('common.delete')}</span>,
        icon: DeleteIcon,
        key: 'delete',
      },
    ];
  }, [navigate, t]);

  const viewOrder = useCallback(
    (params: ICellRendererParams<IOrder>) => {
      navigate(`${ROUTES.ORDER.LISTING}/${params.data.id}`);
    },
    [navigate]
  );

  const isColumnSortable = useCallback((field: string) => {
    return filterableModules.order.sortable.includes(field);
  }, []);

  const orderColDefs: IColDef[] = useMemo(() => {
    return [
      {
        field: 'trackingNumber',
        headerName: t('ordersPage.trackingNumber'),
        sortable: isColumnSortable('trackingNumber'),
        unSortIcon: isColumnSortable('trackingNumber'),
        tooltipField: 'trackingNumber',
        minWidth: 200,
        flex: 1,
        type: 'string',
        visible: true,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'status',
        headerName: t('ordersPage.status'),
        sortable: isColumnSortable('status'),
        unSortIcon: isColumnSortable('status'),
        visible: true,
        minWidth: 200,
        flex: 1,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          switch (params.value) {
            case 'Draft':
              return (
                <span className="grey-chip block w-full">{t('ordersPage.statusValues.draft')}</span>
              );
            case 'Submitted':
              return (
                <span className="primary-chip block w-full">
                  {t('ordersPage.statusValues.submitted')}
                </span>
              );
            case 'Cancelled':
              return (
                <span className="error-chip block w-full">
                  {t('ordersPage.statusValues.cancelled')}
                </span>
              );
            case 'InTransit':
              return (
                <span className="warning-chip block w-full">
                  {t('ordersPage.statusValues.inTransit')}
                </span>
              );
            case 'Pending':
              return (
                <span className="warning-chip block w-full">
                  {t('ordersPage.statusValues.pending')}
                </span>
              );
            case 'Assigned':
              return (
                <span className="primary-chip block w-full">
                  {t('ordersPage.statusValues.assigned')}
                </span>
              );
            case 'Completed':
              return (
                <span className="success-chip block w-full">
                  {t('ordersPage.statusValues.completed')}
                </span>
              );
            default:
              break;
          }
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'collectionCompanyName',
        headerName: t('ordersPage.collectionComponyName'),
        sortable: isColumnSortable('collectionCompanyName'),
        unSortIcon: isColumnSortable('collectionCompanyName'),
        visible: true,
        type: 'string',
        minWidth: 300,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'deliveryCompanyName',
        headerName: t('ordersPage.deliveryComponyName'),
        sortable: isColumnSortable('deliveryCompanyName'),
        unSortIcon: isColumnSortable('deliveryCompanyName'),
        visible: true,
        type: 'string',
        minWidth: 300,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'createdAt',
        type: 'date',
        headerName: t('ordersPage.dateSubmitted'),
        minWidth: 200,
        flex: 1,
        sortable: isColumnSortable('createdAt'),
        unSortIcon: isColumnSortable('createdAt'),
        visible: true,
        cellRenderer: (params: { value: string }) => {
          const value = params.value ? dateFormatter(params.value) : '';
          return searchText ? highlightText(value, searchText) : value;
        },
      },
      {
        field: 'customerName',
        headerName: t('ordersPage.customerName'),
        sortable: isColumnSortable('customerName'),
        unSortIcon: isColumnSortable('customerName'),
        minWidth: 200,
        flex: 1,
        visible: true,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'scheduledCollectionTime',
        headerName: t('ordersPage.collectionTime'),
        sortable: isColumnSortable('scheduledCollectionTime'),
        unSortIcon: isColumnSortable('scheduledCollectionTime'),
        minWidth: 200,
        flex: 1,
        visible: true,
        type: 'date',
        cellRenderer: (params: { value: string }) => {
          const value = params.value ? dateFormatter(params.value) : '';
          return searchText ? highlightText(value, searchText) : value;
        },
      },
      {
        field: 'scheduledDeliveryTime',
        headerName: t('ordersPage.deliveryTime'),
        sortable: isColumnSortable('scheduledDeliveryTime'),
        unSortIcon: isColumnSortable('scheduledDeliveryTime'),
        minWidth: 200,
        flex: 1,
        visible: true,
        type: 'date',
        cellRenderer: (params: { value: string }) => {
          const value = params.value ? dateFormatter(params.value) : '';
          return searchText ? highlightText(value, searchText) : value;
        },
      },
      {
        field: 'serviceLevel',
        headerName: t('ordersPage.serviceLevel'),
        sortable: isColumnSortable('serviceLevel'),
        unSortIcon: isColumnSortable('serviceLevel'),
        minWidth: 200,
        flex: 1,
        visible: true,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'assignedDriverName',
        headerName: t('ordersPage.assignee'),
        sortable: isColumnSortable('assignedDriver'),
        unSortIcon: isColumnSortable('assignedDriver'),
        minWidth: 200,
        flex: 1,
        visible: true,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          return params.value ? (
            searchText ? (
              highlightText(params.value, searchText)
            ) : (
              params.value
            )
          ) : (
            <span className="error-chip">{'Unassigned'}</span>
          );
        },
      },
      {
        field: 'action',
        headerName: t('ordersPage.action'),
        pinned: 'right',
        width: 70,
        sortable: false,
        resizable: false,
        cellRenderer: (params: ICellRendererParams<IOrder>) => {
          return (
            <div className="flex gap-2 h-full items-center justify-start w-full overflow-hidden">
              {params.data.status != 'Draft' && (
                <Icon
                  component={() => <EyeIcon bool={false} />}
                  onClick={() => {
                    viewOrder(params);
                  }}
                  className="cursor-pointer"
                />
              )}
              <Icon
                component={() => <EditPopupIcon bool={false} />}
                onClick={() => {
                  navigate(ROUTES.ORDER.ORDER_EDIT.replace(':id', params.data.id as string));
                }}
                className="cursor-pointer"
              />
            </div>
          );
        },
        visible: true,
      },
    ];
  }, [isColumnSortable, searchText, viewOrder, t]);

  on('columnManager:changed', (data) => {
    if (data.gridName === GridNames.customerPortalOrderGrid && gridRef.current?.api) {
      const columnOrder = data.gridState.map((column: { id: string }) => column.id);
      gridRef.current.api.moveColumns(columnOrder, 0);
    }
  });

  const searchHandler = useCallback((value: string) => {
    setSearchText(value);
    setFilterParams((prev) => ({
      ...prev,
      pageNumber: value ? 1 : prev.pageNumber,
      searchTerm: value || undefined,
    }));
  }, []);

  const clearAllFunctionRef = useRef<{ handleClearAll: () => void }>({
    handleClearAll: () => {},
  });

  const applyFilters = useCallback(
    async (data: { filters: IAssignedFilters[] }) => {
      const filterObject = await advanceFilterObjectMapper(data.filters);

      data.filters.length > 0 &&
        setFilterParams({
          pageNumber: filterParams.pageNumber,
          pageSize: filterParams.pageSize,
          searchTerm: filterParams.searchTerm,
          sortDirection: filterParams.sortDirection,
          ...filterObject,
        });

      setSelectedQuickFilterData(
        data.filters.length > 0 ? (maskQuickFilterData(data.filters) as IAssignedFilters[]) : []
      );
    },
    [filterParams]
  );

  const clearAllToDefault = () => {
    setFilterParams({
      pageNumber: filterParams.pageNumber,
      pageSize: filterParams.pageSize,
      searchTerm: filterParams.searchTerm,
      sortDirection: filterParams.sortDirection,
    });
    setSelectedQuickFilterData([]);
    clearAllFunctionRef.current.handleClearAll();
  };

  return (
    <>
      <div className={`h-[93%] px-5`}>
        <header className="flex justify-between items-center gap-3">
          <div className=" text-[#090A1A] font-semibold text-3xl self-end">
            {t('ordersPage.orderList')}
          </div>
          <div className="flex justify-end items-center gap-3">
            <SearchFilterComponent
              colDefs={orderColDefs}
              isSetQuickFilter
              searchInputPlaceholder={t('ordersPage.searchOrder')}
              onSearch={searchHandler}
              setQuickFilters={() => {}}
              onFilterApply={applyFilters}
              setSelectedQuickFilterData={setSelectedQuickFilterData}
              supportedFields={filterableModules.order.advanceFilter}
              clearAllFunctionRef={clearAllFunctionRef}
              setFilterParams={setFilterParams}
              quickFilterEventKey={'CustomerPortalOrderQuickFilter'}
              quickFilterSettingsKey={'CustomerPortalOrderQuickFilter'}
              quickFilterTitleEventKey={'CustomerPortalOrderQuickFilterTitleEvent'}
            />
            <ColumnManage colDefs={orderColDefs} gridName={GridNames.customerPortalOrderGrid} />
            <div className="pt-5">
              <Divider type="vertical" className="h-[40px] !m-0" />
            </div>
            <div className="flex gap-2 pt-5">
              <QuickFilter
                eventKey={'CustomerPortalOrderQuickFilter'}
                quickFilterTitleEventKey={'CustomerPortalOrderQuickFilterTitleEvent'}
                clearAllToDefault={clearAllToDefault}
                setFilterParams={setFilterParams}
              />
            </div>
          </div>
        </header>
        <main
          className={`${selectedQuickFilterData.length > 0 ? '!h-full overflow-y-hidden' : 'h-full'} overflow-x-hidden overflow-y-auto bg-white`}
        >
          <ActiveFilters
            selectedQuickFilterData={selectedQuickFilterData}
            clearAllToDefault={clearAllToDefault}
            colDefs={orderColDefs}
            className={'pt-5'}
          />
          <div className="mx-auto h-full flex justify-center items-center pt-5">
            <CustomAgGrid
              rowData={orders}
              gridRef={gridRef}
              columnDefs={orderColDefs}
              paginationProps={{
                ...paginationData,
                onPaginationChange(page, pageLimit) {
                  setFilterParams((prev) => ({
                    ...prev,
                    pageNumber: page,
                    pageSize: pageLimit,
                  }));
                },
              }}
              onSortChanged={(params: IExtendedSortChangedEvent) =>
                setFilterParams(onSortChangeHandler(params, filterParams) as typeof filterParams)
              }
              loading={isFetching || isLoading}
              isContextMenu
              contextMenuItem={ordersContextMenuItems}
              className={`3xsm:!h-[62vh] md:!h-[72vh] ${selectedQuickFilterData.length > 0 ? 'lg:!h-[69vh]' : 'lg:!h-[74vh]'}`}
              gridName={GridNames.customerPortalOrderGrid}
              emptyState={{
                title:
                  searchText || selectedQuickFilterData.length > 0
                    ? t('common.noMatchesFound')
                    : t('ordersPage.emptyState.title'),
                description:
                  searchText || selectedQuickFilterData.length > 0
                    ? ''
                    : t('ordersPage.emptyState.description'),
              }}
            />
          </div>
        </main>
      </div>
    </>
  );
};

export default OrderListPage;
