import { ordersServiceHook } from '@/api/order/useOrders';
import { EditPopupIcon, EmailOutlinedIcon, PhoneOutlinedIcon, PrinterIcon } from '@/assets';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import Icon from '@ant-design/icons';
import { Button, Divider, Form, Image, Input, Steps } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import './orderDetails.css';
import { useLanguage } from '@/hooks/useLanguage';
import { dateFormatter } from '@/lib/helper/dateHelper';
import { CustomCollapse } from '@/components/common/customCollaps/CustomCollapse';
import CustomModal from '@/components/common/modal/CustomModal';
import { useMemo, useState } from 'react';
import TextArea from 'antd/es/input/TextArea';
import NotFound404 from '@/components/common/statusFallbackPage/NotFound404';
import { ROUTES } from '@/constant/RoutesConstant';

const OrderDetailsPage = () => {
  const { id: OrderId } = useParams();
  const { t } = useLanguage();

  const [isTrackingModelOpen, setIsTrackingModelOpen] = useState(false);

  const { data: orderDetails, error } = ordersServiceHook.useEntity(OrderId as string, {
    enabled: Boolean(OrderId),
  });
  console.log({ orderDetails });

  const orderSummaryColumn = {
    dataArray: [
      {
        key: 'Level of service',
        label: 'Level of service',
        value: '7888015 canada inc 2 hr charge',
      },
      {
        key: 'description',
        label: 'Description',
        value: '1 Envelop + 2 box + 3 skid = Total 6',
      },
      {
        key: 'weight',
        label: 'weight (Kg)',
        value: '222',
      },
      {
        key: 'totalPrice',
        label: 'Total cost',
        value: `$${orderDetails?.totalPrice}`,
      },
    ],
  };
  const trackingDetails = {
    dataArray: [
      {
        key: 'trackingNo',
        label: t('ordersPage.orderInfoDataFields.trackingNo'),
        value: orderDetails?.trackingNumber || 'N/A',
      },
      {
        key: 'dueDate',
        label: 'Due date',
        value: dateFormatter(orderDetails?.createdAt as string) || 'N/A',
      },
      {
        key: 'dateSubmitted',
        label: 'Date submitted',
        value: dateFormatter(orderDetails?.createdAt as string) || 'N/A',
      },
      {
        key: 'collectionFrom',
        label: 'Collection from',
        value: orderDetails?.collectionContactName || 'N/A',
      },
      {
        key: 'deliveryTo',
        label: 'Delivery to',
        value: orderDetails?.deliveryContactName || 'N/A',
      },
      {
        key: 'requestBy',
        label: 'Request by',
        value: orderDetails?.requestedByName || 'N/A',
      },
      {
        key: 'deliveryOn',
        label: 'Delivery on',
        value: orderDetails?.actualDeliveryTime
          ? dateFormatter(orderDetails?.actualDeliveryTime as string)
          : 'N/A',
      },
      {
        key: 'DeliveredBy',
        label: 'Delivered by',
        value: 'Alex Ray',
      },
      {
        key: 'CollectionSignature',
        label: 'Pickup signature',
        value: 'Signature',
      },

      {
        key: 'DeliverySignature',
        label: 'Delivery signature',
        value: 'Signature',
      },
    ],
    images: true,
  };

  const collapseDetailsRendered = (obj: any): React.ReactNode => {
    return (
      <>
        <div className="grid lg:grid-cols-2 w-full gap-2">
          {obj.dataArray?.map((item: { label: string; value: string }, index: number) => {
            return (
              <div className="flex w-full p-1">
                <div key={index} className=" flex items-center lg:w-[25%] w-full">
                  <span className="w-48 flex items-center gap-1 font-medium text-primary-900">
                    {item.label}:
                  </span>
                </div>
                <div className="lg:w-[75%] w-full text-primary-900 font-medium">{item.value}</div>
              </div>
            );
          })}
          {obj.images && (
            <div className="mt-4">
              <h2 className="font-semibold text-primary-900 text-base">Uploaded images</h2>
              <div className="mt-4">
                <div className="border border-primary-100 rounded-lg w-fit p-2">
                  <Image
                    src="https://i.etsystatic.com/12671181/r/il/e167c9/2284200167/il_570xN.2284200167_1w5u.jpg"
                    alt=""
                    width={72}
                    height={72}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </>
    );
  };

  const itemsForOrderDetails = [
    {
      key: 'trackingDetails',
      label: 'Tracking details',
      children: collapseDetailsRendered(trackingDetails) as React.ReactNode,
    },
    {
      key: 'orderSummary',
      label: 'Order summary',
      children: collapseDetailsRendered(orderSummaryColumn) as React.ReactNode,
    },
  ];

  const collectionLocationDetailsRendered = () => {
    return (
      <div className="flex flex-col gap-1 pt-1.5">
        <h2 className="text-primary-900 font-semibold text-text-md">{'Life - Life Corporation'}</h2>
        <h2 className="text-primary-900 font-semibold text-text-md">
          {orderDetails?.collectionContactName}
        </h2>
        <div className="flex items-start lg:items-center text-primary-900 font-medium text-text-md leading-8 flex-col lg:flex-row">
          <div className="flex gap-1 items-center font-medium">
            <PhoneOutlinedIcon /> {'(************* '} ext. {'1234'}
          </div>
          <Divider
            type="vertical"
            style={{ borderColor: '#8080805e', height: '20px' }}
            className="hidden lg:block"
          />
          <div className="flex gap-1.5 items-center font-medium">
            <EmailOutlinedIcon /> {'<EMAIL>'}
          </div>
        </div>
        <div className="text-text-md text-primary-900">
          123 Maple Street, Apt 305, Maple Leaf Towers, Toronto, ON, Canada, BC V6B 3K9
        </div>
      </div>
    );
  };

  const deliveryLocationDetailsRendered = () => {
    return (
      <div className="flex flex-col gap-1 pt-1.5">
        <h2 className="text-primary-900 font-semibold text-text-md">{'Life - Life Corporation'}</h2>
        <h2 className="text-primary-900 font-semibold text-text-md">
          {orderDetails?.deliveryContactName}
        </h2>
        <div className="flex items-start lg:items-center text-primary-900 font-medium text-text-md leading-8 flex-col lg:flex-row">
          <div className="flex gap-1 items-center font-medium">
            <PhoneOutlinedIcon /> {'(************* '} ext. {'1234'}
          </div>
          <Divider
            type="vertical"
            style={{ borderColor: '#8080805e', height: '20px' }}
            className="hidden lg:block"
          />
          <div className="flex gap-1 items-center font-medium">
            <EmailOutlinedIcon /> {'<EMAIL>'}
          </div>
        </div>
        <div className="text-text-md text-primary-900">
          123 Maple Street, Apt 305, Maple Leaf Towers, Toronto, ON, Canada, BC V6B 3K9
        </div>
      </div>
    );
  };
  const navigate = useNavigate();

  const Footer = useMemo(
    () => (
      <footer className="custom-modals-footer flex gap-2 justify-end">
        <Button
          className="rounded-lg border-[#96A9B1]"
          onClick={() => setIsTrackingModelOpen(false)}
        >
          {t('common.cancel')}
        </Button>
        <Button
          form="tracking-link-form"
          htmlType="submit"
          type="primary"
          // loading={isFetching || isLoading}
          className="bg-primary-600 text-white border-0 rounded-lg hover:!bg-primary-600"
          onClick={() =>
            navigate(
              ROUTES.ORDER.ORDER_TRACKING.replace(':id', orderDetails?.trackingNumber as string)
            )
          }
        >
          {'Send'}
        </Button>
      </footer>
    ),
    [navigate, orderDetails?.trackingNumber, t]
  );

  const [form] = Form.useForm();
  const messageWatcher = Form.useWatch('message', form);

  return (
    <>
      <CustomModal
        modalTitle={'Send Tracking Link'}
        modalDescription={'Fill the email address and write a message with it.'}
        open={isTrackingModelOpen}
        onCancel={() => setIsTrackingModelOpen(false)}
        footer={Footer}
        destroyOnClose
        keyboard={false}
      >
        <Form
          scrollToFirstError={{ behavior: 'smooth' }}
          name="tracking-link-form"
          layout="vertical"
          form={form}
          className="custom-form"
          preserve={false}
        >
          <div className="flex flex-col gap-2">
            <Form.Item
              validateFirst
              label="Email address"
              name="email"
              rules={[
                {
                  required: true,
                  message: 'Email is required',
                },
                { type: 'email', message: 'Please enter a valid email' },
              ]}
            >
              <Input placeholder="Enter email" maxLength={70} />
            </Form.Item>
            <Form.Item label="Message" name="message">
              <TextArea placeholder="Enter message" maxLength={500} className="!min-h-[200px]" />
            </Form.Item>
          </div>
          <span className="block mt-4">Message: {messageWatcher}</span>
        </Form>
      </CustomModal>
      {error ? (
        <NotFound404
          title="Order Not Found"
          description="The order you are looking for does not exist."
          extraNode={
            <Button
              className="h-[40px] px-8 hover:!text-white hover:!bg-primary-600"
              onClick={() => navigate(ROUTES.ORDER.LISTING)}
            >
              Go Back
            </Button>
          }
        />
      ) : (
        <div className="flex print:!max-h-fit max-h-[87.5vh] overflow-y-scroll flex-col gap-4 px-6">
          <header className="flex justify-between items-center gap-3 pt-5">
            <div className="flex flex-col gap-1">
              <PageHeadingComponent
                title={`Order details : #${orderDetails?.trackingNumber || 'N/A'}`}
                onBackClick={() => window.history.back()}
                isChildComponent
              />
              <h3 className="leading-6 font-medium text-primary-900 print:!ml-0 ml-12">
                Your request has been submitted successfully.
              </h3>
            </div>
            <div className="print:!hidden text-center bg-primary-600 print:!bg-primary-600 font-semibold py-2.5 print:!border px-14 text-[#e5e7eb] rounded-lg">
              {'Approve'}
            </div>
          </header>
          <div className="print:!hidden flex gap-4 w-full flex-wrap justify-end">
            <Button
              className="h-[44px] px-8 hover:!text-white hover:!bg-primary-600 font-medium"
              onClick={() => setIsTrackingModelOpen(true)}
            >
              Send Tracking Link
            </Button>
            <Button className="h-[44px] px-8 text-primary-900 hover:!text-white hover:!bg-primary-600 font-medium">
              <Icon component={() => <EditPopupIcon bool={false} />} /> Edit Order
            </Button>
            <Button
              className="h-[44px] px-8 text-primary-900 hover:!text-white hover:!bg-primary-600 font-medium"
              onClick={() => {
                document.fonts.ready.then(() => {
                  window.print();
                });
              }}
            >
              <Icon component={() => <PrinterIcon bool={false} />} />
              Print This Page
            </Button>
            <Button
              className="h-[44px] px-8 text-primary-900 hover:!text-white hover:!bg-primary-600 font-medium"
              onClick={() => navigate('/shipping')}
            >
              <Icon component={() => <PrinterIcon bool={false} />} />
              Print Shipping Label
            </Button>
            <Button
              className="h-[44px] px-8 text-primary-900 hover:!text-white hover:!bg-primary-600 font-medium"
              onClick={() => navigate('/waybill')}
            >
              <Icon component={() => <PrinterIcon bool={false} />} />
              Print Way Bill
            </Button>
            <Button
              className="h-[44px] px-8 text-primary-900 hover:!text-white hover:!bg-primary-600 font-medium"
              onClick={() => navigate('/bill')}
            >
              <Icon component={() => <PrinterIcon bool={false} />} />
              Print Bill Of Lading
            </Button>
          </div>
          <div className="border-[1px] border-[#BFC2DF] rounded-[8px] p-2 sm:p-4">
            <Steps
              size="small"
              current={1}
              items={[
                {
                  title: 'Submitted',
                  description: (
                    <div className="flex flex-col w-full text-primary-300">
                      <span className="text-[12px]">Submitted an order at</span>
                      <span className="text-[12px]">23/12/2024 10:00 AM</span>
                    </div>
                  ),
                },
                {
                  title: 'Assigned',
                  description: (
                    <div className="flex flex-col gap-2 w-full text-primary-300">
                      <span className="text-[12px]">
                        A driver will be assigned to your order soon.
                      </span>
                    </div>
                  ),
                },
                {
                  title: 'In transit',
                  description: (
                    <div className="flex flex-col gap-2 w-full text-primary-300">
                      <span className="text-[12px]">
                        Once assigned, the driver will pick up your order for delivery.
                      </span>
                    </div>
                  ),
                },
                {
                  title: 'Completed',
                  description: (
                    <div className="flex flex-col gap-2 w-full text-wrap text-primary-300">
                      <span className="text-wrap text-[12px]">
                        Your order will be marked as delivered once it reaches its destination.
                      </span>
                    </div>
                  ),
                },
              ]}
              className="orders-stepper !border-none "
            />
          </div>
          <main className="pb-4">
            <CustomCollapse
              defaultActiveKey={['trackingDetails', 'orderSummary']}
              items={itemsForOrderDetails}
            />
            <div className="flex gap-4 mt-4">
              <CustomCollapse
                defaultActiveKey={['delivery']}
                className="w-1/2"
                collapseProps={{ collapsible: 'icon' }}
                collapsePanelProps={{
                  showArrow: false,
                  key: 'delivery',
                  header: 'Delivery location',
                }}
                items={[
                  {
                    key: 'delivery',
                    label: 'Delivery location',
                    children: deliveryLocationDetailsRendered(),
                  },
                ]}
              />
              <CustomCollapse
                defaultActiveKey={['pickup']}
                className="w-1/2"
                collapseProps={{ collapsible: 'icon' }}
                collapsePanelProps={{
                  showArrow: false,
                  key: 'pickup',
                  header: 'Pickup location',
                }}
                items={[
                  {
                    key: 'pickup',
                    label: 'Pickup location',
                    children: collectionLocationDetailsRendered(),
                  },
                ]}
              />
            </div>
          </main>
        </div>
      )}
    </>
  );
};

export default OrderDetailsPage;
