import { ordersServiceHook } from '@/api/order/useOrders';
import { PhoneOutlinedIcon } from '@/assets';
import { CustomCollapse } from '@/components/common/customCollaps/CustomCollapse';
import NotFound404 from '@/components/common/statusFallbackPage/NotFound404';
import { useConfig } from '@/contexts/ConfigContext';
import { useLanguage } from '@/hooks/useLanguage';
import { getOrderStatusClassName } from '@/lib/helper';
import { dateFormatter } from '@/lib/helper/dateHelper';
import Icon, { LoadingOutlined } from '@ant-design/icons';
import { Image, Spin, Steps } from 'antd';
import { useParams } from 'react-router-dom';

const OrderTracking = () => {
  const { id: trackingNumber } = useParams();
  const { t } = useLanguage();
  const { config } = useConfig();

  const {
    data: trackingDetails,
    error,
    isFetching,
  } = ordersServiceHook.useEntity(`tracking/${trackingNumber}` as string, {
    enabled: Boolean(trackingNumber),
  });

  const orderTrackingDetailsObject = {
    dataArray: [
      {
        key: `totalWeight`,
        label: `${t('ordersPage.orderInfoDataFields.weight')}(${config.units?.weight})`,
        value: trackingDetails?.totalWeight || 'N/A',
      },
      {
        key: 'description',
        label: 'Description',
        value: trackingDetails?.description || 'N/A',
      },
      {
        key: 'collectionFrom',
        label: 'Collection from',
        value: trackingDetails?.collectionContactName || 'N/A',
      },
      {
        key: 'deliveryTo',
        label: 'Delivery to',
        value: trackingDetails?.deliveryContactName || 'N/A',
      },
      {
        key: 'CollectionSignature',
        label: 'Collection signature',
        value: 'Signature',
      },
      {
        key: 'DeliverySignature',
        label: 'Delivery signature',
        value: 'Signature',
      },
    ],
    images: true,
  };

  const collapseDetailsRendered = (obj: any): React.ReactNode => {
    return (
      <>
        <div className="grid lg:grid-cols-2 w-full gap-2">
          {obj?.dataArray?.map((item: { label: string; value: string }, index: number) => {
            return (
              <div className="flex w-full p-1">
                <div key={index} className=" flex items-center lg:w-[25%] w-full">
                  <span className="w-48 flex items-center gap-1 font-medium text-primary-900">
                    {item.label}:
                  </span>
                </div>
                <div className="lg:w-[75%] w-full text-primary-900 font-medium">{item.value}</div>
              </div>
            );
          })}
          {obj.images && (
            <div className="mt-4">
              <h2 className="font-semibold text-primary-900 text-base">Uploaded images</h2>
              <div className="mt-4">
                <div className="border border-primary-100 rounded-lg w-fit p-2">
                  <Image
                    src="https://i.etsystatic.com/12671181/r/il/e167c9/2284200167/il_570xN.2284200167_1w5u.jpg"
                    alt=""
                    width={72}
                    height={72}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </>
    );
  };

  const itemsForOrderDetails = [
    {
      key: 'orderDetails',
      label: 'Order details',
      children: collapseDetailsRendered(orderTrackingDetailsObject) as React.ReactNode,
    },
  ];

  return (
    <>
      {error ? (
        <NotFound404
          title="Order Not Found"
          description="The order you are looking for does not exist."
        />
      ) : (
        <div className=" flex flex-col gap-4 p-8 text-primary-900">
          <div className="flex justify-between items-center">
            <div className="text-lg font-semibold md:text-3xl">
              Tracking number: {trackingNumber || 'N/A'}
            </div>
            <div className="flex bg-primary-25 border border-primary-50 p-3 rounded-lg">
              <div className="flex gap-1 flex-col border-r border-r-primary-100 pr-4 ">
                <span className="font-semibold">Status</span>
                <span
                  className={`${getOrderStatusClassName(trackingDetails?.status as string)} text-sm`}
                >
                  {trackingDetails?.status || 'N/A'}
                </span>
              </div>
              <div className="flex gap-1 flex-col pl-4">
                <span className="font-semibold">Estimated time</span>
                {trackingDetails?.estimatedDuration
                  ? dateFormatter(trackingDetails?.estimatedDuration)
                  : 'N/A'}
              </div>
            </div>
          </div>
          <div className="border-[1px] border-[#BFC2DF] rounded-[8px] p-2 sm:p-4">
            <Steps
              size="small"
              current={1}
              items={[
                {
                  title: 'Submitted',
                  description: (
                    <div className="flex flex-col w-full text-primary-300">
                      <span className="text-[12px]">Submitted an order at</span>
                      <span className="text-[12px]">23/12/2024 10:00 AM</span>
                    </div>
                  ),
                },
                {
                  title: 'Assigned',
                  description: (
                    <div className="flex flex-col gap-2 w-full text-primary-300">
                      <span className="text-[12px]">
                        Alex has been assigned to deliver this order on 24/12/205, at 4:00 PM.
                      </span>
                    </div>
                  ),
                },
                {
                  title: 'In transit',
                  description: (
                    <div className="flex flex-col gap-2 w-full text-primary-300">
                      <span className="text-[12px]">
                        By 5:00 PM, Alex picks up the order and starts the delivery.
                      </span>
                    </div>
                  ),
                },
                {
                  title: 'Completed',
                  description: (
                    <div className="flex flex-col gap-2 w-full text-wrap text-primary-300">
                      <span className="text-wrap text-[12px]">
                        At 3:30 PM, Alex delivers the package to the customer’s doorstep.
                      </span>
                    </div>
                  ),
                },
              ]}
              className="orders-stepper !border-none"
            />
          </div>
          <CustomCollapse
            defaultActiveKey={['orderDetails']}
            items={itemsForOrderDetails}
            collapseProps={{ collapsible: 'icon' }}
            collapsePanelProps={{
              showArrow: false,
              key: 'orderDetails',
              header: 'Order details',

              extra: isFetching ? (
                <Spin
                  indicator={
                    <div className="text-primary-600 !h-fit !w-fit">
                      <LoadingOutlined spin className="text-primary-600 block" />
                    </div>
                  }
                  size="small"
                />
              ) : (
                ''
              ),
            }}
          />
          <div className="flex justify-center gap-2">
            Customer support:
            <span className=" font-semibold">
              <Icon component={PhoneOutlinedIcon} className="mr-2" />
              (*************
            </span>
          </div>
        </div>
      )}
    </>
  );
};

export default OrderTracking;
