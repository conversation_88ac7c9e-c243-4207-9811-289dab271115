import { logout, useGetCurrentUser } from '@/api/auth/auth.service';
import {
  OrderEntrySelectedIcon,
  OrderEntryIcon,
  OrderListIcon,
  AddressesIcon,
  ContactsIcon,
  LumigoLogo,
  LogoutIcon,
} from '@/assets';
import { InvoicesIcon } from '@/assets/icons/invoicesIcon';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { ROUTES } from '@/constant/RoutesConstant';
import { useLanguage } from '@/hooks/useLanguage';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { removeStorageItem } from '@/lib/Storage';
import { StorageKeys } from '@/types/enums/StorageEnums';
import { UserOutlined, LockOutlined, LogoutOutlined } from '@ant-design/icons';
import { Dropdown, MenuProps } from 'antd';
import React, { useMemo, useRef, useState } from 'react';

interface NavLink {
  title: string;
  path: string;
  active: boolean;
  icon: React.ReactNode;
}

interface HeaderProps {
  showLogoOnly?: boolean;
}

const Header = (props: HeaderProps) => {
  const handleMenuClick = (info: { key: string }) => {
    if (info.key === 'logout') {
      logoutHandler({});
    }
  };

  const { t } = useLanguage();
  const { navigate } = useNavigationContext();

  const handleNavigation = (path: string) => {
    navigate(path);
    setMobileMenuOpen(false);
  };

  const getAvatarText = (contactName?: string): string => {
    if (!contactName) return 'NA';

    const words = contactName.split(' ').filter(Boolean);

    if (words.length === 1) return words[0].charAt(0).toUpperCase();

    return `${words[0].charAt(0).toUpperCase()}${words[1].charAt(0).toUpperCase()}`;
  };

  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const mobileMenuRef = useRef<HTMLDivElement>(null);
  const { data: userInfo } = useGetCurrentUser({ enabled: !props.showLogoOnly });
  const currentPath = window.location.pathname;

  const profileMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      label: 'Profile',
      icon: <UserOutlined />,
    },
    {
      key: 'reset-password',
      label: 'Reset password',
      icon: <LockOutlined />,
    },
    {
      key: 'logout',
      label: 'Logout',
      icon: <LogoutOutlined />,
      onClick: () => logoutHandler({}),
    },
  ];

  const navLinks: NavLink[] = useMemo(() => {
    const links = [
      {
        title: 'Order entry',
        path: ROUTES.ORDER.ORDER_ENTRY,
        active: currentPath === ROUTES.ORDER.ORDER_ENTRY,
        icon: (
          <span className="mr-1 pt-[1px]">
            {currentPath === ROUTES.ORDER.ORDER_ENTRY ? (
              <OrderEntrySelectedIcon />
            ) : (
              <OrderEntryIcon bool={false} />
            )}
          </span>
        ),
      },
      {
        title: 'Order list',
        path: ROUTES.ORDER.LISTING,
        active: currentPath.includes(ROUTES.ORDER.LISTING),
        icon: (
          <span className="mr-1 pt-[1px]">
            <OrderListIcon bool={currentPath.includes(ROUTES.ORDER.LISTING)} />{' '}
          </span>
        ),
      },
    ];

    if (userInfo?.permissions?.address) {
      links.push({
        title: 'Addresses',
        path: ROUTES.ADDRESSES.LISTING || '/addresses',
        active:
          currentPath.includes(ROUTES.ADDRESSES.LISTING) || currentPath.includes('/addresses'),
        icon: (
          <span className="mr-1 pt-[1px]">
            <AddressesIcon bool={currentPath.includes(ROUTES.ADDRESSES.LISTING)} />{' '}
          </span>
        ),
      });
    }

    if (userInfo?.permissions?.invoices) {
      links.push({
        title: 'Invoices',
        path: '/invoices',
        active: currentPath.includes('/invoices'),
        icon: (
          <span className="mr-1 pt-[1px]">
            <InvoicesIcon bool={currentPath.includes(ROUTES.INVOICES.LISTING)} />{' '}
          </span>
        ),
      });
    }

    if (userInfo?.isPrimary) {
      links.push({
        title: 'Contact',
        path: '/contact',
        active: currentPath.includes('/contact'),
        icon: (
          <span className="mr-1 pt-[1px]">
            <ContactsIcon bool={currentPath.includes(ROUTES.CONTACT.LISTING)} />{' '}
          </span>
        ),
      });
    }

    return links;
  }, [currentPath, userInfo?.permissions, userInfo?.isPrimary]);

  const logoutHandler = (
    e: React.MouseEvent | { preventDefault?: () => void; stopPropagation?: () => void }
  ) => {
    if (e.preventDefault && e.stopPropagation) {
      e.preventDefault();
      e.stopPropagation();
    }
    setMobileMenuOpen(false);

    customAlert.warning({
      title: t('auth.logoutConfirmation'),
      message: t('auth.redirectTxt'),
      firstButtonFunction: async () => {
        try {
          console.log('Attempting to logout');
          await logout();
          customAlert.destroy();
          window.location.href = ROUTES.COMMON.LOGIN;
        } catch (error) {
          console.error('Logout failed:', error);
          removeStorageItem(StorageKeys.USER_INFO);
          removeStorageItem(StorageKeys.IS_AUTHENTICATED);
          window.location.href = ROUTES.COMMON.LOGIN;
        }
      },
      secondButtonFunction: () => customAlert.destroy(),
      firstButtonTitle: t('common.logout'),
      secondButtonTitle: t('common.cancel'),
    });
  };

  return (
    <header className="app-header">
      <div className="header-container">
        <div className={`header-logo ${props.showLogoOnly && '!w-full'}`}>
          <div className={`logo-wrapper ${props.showLogoOnly && 'logo-wrapper-without-content'}`}>
            <img
              src={LumigoLogo}
              alt="Logo"
              onClick={() => !props.showLogoOnly && navigate(ROUTES.ORDER.ORDER_ENTRY)}
              className="cursor-pointer"
            />
          </div>
        </div>
        {!props.showLogoOnly && (
          <>
            <div className="md:hidden print:!hidden">
              <button
                type="button"
                className="mobile-menu-button mobile-menu-toggle"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                <svg
                  className="h-6 w-6"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              </button>
            </div>

            <div className="hidden md:flex flex-1 justify-center">
              <nav className="flex">
                {navLinks.map((link, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={() => handleNavigation(link.path)}
                    className={`flex items-center px-4 py-4 font-medium cursor-pointer border-0 bg-transparent ${
                      link.active
                        ? 'text-primary-600 border-b-2 border-primary-600'
                        : 'text-gray-600'
                    }`}
                  >
                    {link.icon}
                    {link.title}
                  </button>
                ))}
              </nav>
            </div>

            <div className="hidden print:!hidden md:flex items-center ml-4 relative">
              <Dropdown
                menu={{
                  items: profileMenuItems,
                  onClick: handleMenuClick,
                }}
                trigger={['click']}
                placement="bottomRight"
              >
                <div className="flex items-center cursor-pointer profile-toggle">
                  <div className="h-8 w-8 flex items-center justify-center bg-indigo-50 text-indigo-700 text-sm font-medium">
                    {getAvatarText(userInfo?.name)}
                  </div>
                  <span className="px-2 text-gray-700 font-medium hidden sm:inline">
                    {userInfo?.name}
                  </span>
                  <svg
                    className="h-5 w-5 text-gray-400"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </Dropdown>
            </div>

            <div className="md:hidden flex items-center print:!hidden">
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'logout',
                      label: 'Logout',
                      icon: <img src={LogoutIcon} alt="Logout" className="w-5 h-5 mr-1" />,
                      onClick: () => logoutHandler({}),
                    },
                  ],
                  onClick: handleMenuClick,
                }}
                trigger={['click']}
                placement="bottomRight"
              >
                <div className="h-8 w-8 flex items-center justify-center bg-indigo-50 text-indigo-700 text-sm font-medium rounded-full profile-toggle">
                  {getAvatarText(userInfo?.name)}
                </div>
              </Dropdown>
            </div>
          </>
        )}
      </div>

      {!props.showLogoOnly && mobileMenuOpen && (
        <div className="md:hidden" ref={mobileMenuRef}>
          <div className="mobile-menu">
            {navLinks.map((link, index) => (
              <button
                key={index}
                type="button"
                onClick={() => handleNavigation(link.path)}
                className={`mobile-menu-item border-0 bg-transparent ${
                  link.active ? 'active-mobile-link' : 'inactive-mobile-link'
                }`}
              >
                <span className="w-[15px] h-[15px]">{link.icon}</span>
                {link.title}
              </button>
            ))}
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
